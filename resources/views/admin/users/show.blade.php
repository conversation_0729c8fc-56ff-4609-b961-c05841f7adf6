@extends('components.layouts.admin') :title="__('View User')">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">{{ __('View User') }}</h1>
            <p class="text-muted small">{{ __('User details for') }} {{ $user->name }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                {{ __('Back') }}
            </a>
            <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary">
                <i class="fas fa-pencil-alt me-2"></i>
                {{ __('Edit') }}
            </a>
        </div>
    </div>

    <div class="card border-0 shadow-sm">
        <div class="card-body p-4">
            <div class="d-flex align-items-center mb-4">
                <div class="rounded-circle bg-primary-soft d-flex align-items-center justify-content-center me-3" style="width: 64px; height: 64px;">
                    <span class="text-primary fw-bold fs-4">{{ $user->initials() }}</span>
                </div>
                <div>
                    <h2 class="h4 fw-bold mb-1">{{ $user->name }}</h2>
                    <p class="text-muted small mb-0">{{ $user->email }}</p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <h3 class="h5 mb-3">{{ __('Account Information') }}</h3>
                    <div class="mb-4">
                        <div class="mb-3">
                            <label class="form-label fw-medium">{{ __('Role') }}</label>
                            <div>
                                @if ($user->is_admin)
                                    <span class="badge bg-primary">{{ __('Admin') }}</span>
                                @else
                                    <span class="badge bg-secondary">{{ __('User') }}</span>
                                @endif
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-medium">{{ __('Email Verified') }}</label>
                            <div>
                                @if ($user->email_verified_at)
                                    <span class="badge bg-success">{{ __('Verified') }}</span>
                                @else
                                    <span class="badge bg-warning text-dark">{{ __('Not Verified') }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h3 class="h5 mb-3">{{ __('Dates') }}</h3>
                    <div class="mb-4">
                        <div class="mb-3">
                            <label class="form-label fw-medium">{{ __('Joined On') }}</label>
                            <div>{{ $user->created_at->format('F j, Y \a\t g:i A') }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-medium">{{ __('Last Updated') }}</label>
                            <div>{{ $user->updated_at->format('F j, Y \a\t g:i A') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer bg-white d-flex justify-content-between py-3">
            @if ($user->id !== auth()->id())
                <form action="{{ route('admin.users.destroy', $user) }}" method="POST" onsubmit="return confirm('{{ __('Are you sure you want to delete this user?') }}')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        {{ __('Delete User') }}
                    </button>
                </form>
            @else
                <div></div>
            @endif
            <div class="d-flex gap-2">
                <form action="{{ route('admin.users.toggle-admin', $user) }}" method="POST">
                    @csrf
                    @method('PATCH')
                    <button type="submit" class="btn btn-outline-{{ $user->is_admin ? 'warning' : 'info' }}">
                        <i class="fas fa-user-{{ $user->is_admin ? 'minus' : 'plus' }} me-2"></i>
                        {{ $user->is_admin ? __('Remove Admin') : __('Make Admin') }}
                    </button>
                </form>
                <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                    {{ __('Back to List') }}
                </a>
            </div>
        </div>
    </div>
@endsection
