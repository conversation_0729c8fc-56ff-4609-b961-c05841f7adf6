@extends('components.layouts.admin')

@section('title', __('Site Settings'))

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('Site Settings') }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Settings</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Settings List -->
            <div class="row">
                <div class="col-12">
                    <div class="list-group">
                        <!-- Social Media Settings -->
                        <a href="{{ route('admin.settings.social-media') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-share-alt text-info mr-3 fa-lg"></i>
                                <div>
                                    <h6 class="mb-1">{{ __('Social Media Settings') }}</h6>
                                    <small class="text-muted">{{ __('Configure social media links for website footer') }}</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge badge-info mr-2">
                                    {{ App\Models\Setting::where('key', 'LIKE', 'social_%')->whereNotNull('value')->where('value', '!=', '')->count() }}/4 {{ __('configured') }}
                                </span>
                                <i class="fas fa-chevron-right text-muted"></i>
                            </div>
                        </a>

                        <!-- Consultation Status Settings -->
                        <a href="{{ route('admin.consultation-statuses.index') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-tags text-success mr-3 fa-lg"></i>
                                <div>
                                    <h6 class="mb-1">{{ __('Consultation Status Types') }}</h6>
                                    <small class="text-muted">{{ __('Manage consultation workflow statuses') }}</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge badge-success mr-2">{{ App\Models\ConsultationStatus::count() }} {{ __('types') }}</span>
                                <i class="fas fa-chevron-right text-muted"></i>
                            </div>
                        </a>

                        <!-- API Settings -->
                        <a href="{{ route('admin.settings.api') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-shield-alt text-warning mr-3 fa-lg"></i>
                                <div>
                                    <h6 class="mb-1">{{ __('API Security Settings') }}</h6>
                                    <small class="text-muted">{{ __('Configure API access and security settings') }}</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge badge-warning mr-2">
                                    {{ App\Models\Setting::get('allowed_api_ips') ? count(explode(',', App\Models\Setting::get('allowed_api_ips'))) : 0 }} {{ __('IPs allowed') }}
                                </span>
                                <i class="fas fa-chevron-right text-muted"></i>
                            </div>
                        </a>

                        <!-- API Tokens -->
                        <a href="{{ route('admin.settings.api-tokens') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-key text-danger mr-3 fa-lg"></i>
                                <div>
                                    <h6 class="mb-1">{{ __('API Access Tokens') }}</h6>
                                    <small class="text-muted">{{ __('Create and manage API access tokens') }}</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge badge-danger mr-2">{{ \Laravel\Sanctum\PersonalAccessToken::count() }} {{ __('active') }}</span>
                                <i class="fas fa-chevron-right text-muted"></i>
                            </div>
                        </a>
                    </div>
                </div>
            </div>


        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
