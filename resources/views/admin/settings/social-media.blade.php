@extends('components.layouts.admin')

@section('title', __('Social Media Settings'))

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('Social Media Settings') }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.settings.index') }}">Settings</a></li>
                        <li class="breadcrumb-item active">Social Media</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">{{ __('Social Media Settings') }}</h3>
                            <div class="card-tools">
                                <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-arrow-left mr-2"></i>{{ __('Back to Settings') }}
                                </a>
                            </div>
                        </div>
                        <form action="{{ route('admin.settings.social-media.update') }}" method="POST" id="social-media-form">
                            @csrf
                            @method('PUT')
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-12 mb-3">
                                        <div class="alert alert-info" role="alert">
                                            <i class="fas fa-info-circle mr-2"></i>
                                            {{ __('Enter your social media URLs below. Leave a field empty to hide that social media icon on the website.') }}
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    @foreach($socialMediaSettings as $setting)
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="{{ $setting->key }}">
                                                    @if($setting->key == 'social_facebook')
                                                        <i class="fab fa-facebook-f mr-2 text-primary"></i>
                                                    @elseif($setting->key == 'social_twitter')
                                                        <i class="fab fa-twitter mr-2 text-info"></i>
                                                    @elseif($setting->key == 'social_linkedin')
                                                        <i class="fab fa-linkedin-in mr-2 text-primary"></i>
                                                    @elseif($setting->key == 'social_instagram')
                                                        <i class="fab fa-instagram mr-2 text-danger"></i>
                                                    @endif
                                                    {{ $setting->label }}
                                                </label>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">
                                                            <i class="fas fa-link"></i>
                                                        </span>
                                                    </div>
                                                    <input
                                                        type="url"
                                                        class="form-control @error($setting->key) is-invalid @enderror"
                                                        id="{{ $setting->key }}"
                                                        name="{{ $setting->key }}"
                                                        value="{{ old($setting->key, $setting->value) }}"
                                                        placeholder="{{ $setting->description }}"
                                                    >
                                                    @error($setting->key)
                                                        <div class="invalid-feedback">
                                                            {{ $message }}
                                                        </div>
                                                    @enderror
                                                </div>
                                                <small class="form-text text-muted">{{ $setting->description }}</small>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save mr-2"></i> {{ __('Save Changes') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
