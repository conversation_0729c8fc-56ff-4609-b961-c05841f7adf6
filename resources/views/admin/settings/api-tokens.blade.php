@extends('components.layouts.admin')

@section('title', __('API Tokens'))

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('API Tokens') }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.settings.index') }}">Settings</a></li>
                        <li class="breadcrumb-item active">API Tokens</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            @if(session('plain_text_token'))
                <div class="alert alert-success" role="alert">
                    <h5 class="alert-heading"><i class="fas fa-check-circle mr-2"></i> {{ __('Token Created Successfully') }}</h5>
                    <p>{{ __('Your new API token has been created. Please copy it now as it will not be shown again:') }}</p>
                    <div class="input-group mb-2">
                        <input type="text" class="form-control" value="{{ session('plain_text_token') }}" id="tokenInput" readonly>
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" type="button" onclick="copyToken()">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <small class="text-muted">{{ __('Make sure to store this token securely.') }}</small>
                </div>
            @endif

            <div class="row">
                <div class="col-md-5">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">{{ __('Create New Token') }}</h3>
                            <div class="card-tools">
                                <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-arrow-left mr-2"></i>{{ __('Back to Settings') }}
                                </a>
                            </div>
                        </div>
                        <form action="{{ route('admin.settings.api-tokens.store') }}" method="POST" id="token-form">
                            @csrf
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="user_id">{{ __('User') }}</label>
                                    <select class="form-control @error('user_id') is-invalid @enderror" id="user_id" name="user_id" required>
                                        <option value="">{{ __('Select User') }}</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }} ({{ $user->email }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('user_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="token_name">{{ __('Token Name') }}</label>
                                    <input type="text" class="form-control @error('token_name') is-invalid @enderror" id="token_name" name="token_name" value="{{ old('token_name') }}" placeholder="e.g., Production API Token" required>
                                    @error('token_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">{{ __('Give your token a descriptive name for easy identification.') }}</small>
                                </div>

                                <div class="form-group">
                                    <label>{{ __('Token Abilities') }}</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="abilities[]" value="create" id="ability_create" {{ is_array(old('abilities')) && in_array('create', old('abilities')) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="ability_create">
                                            {{ __('Create') }}
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="abilities[]" value="read" id="ability_read" {{ is_array(old('abilities')) && in_array('read', old('abilities')) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="ability_read">
                                            {{ __('Read') }}
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="abilities[]" value="update" id="ability_update" {{ is_array(old('abilities')) && in_array('update', old('abilities')) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="ability_update">
                                            {{ __('Update') }}
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="abilities[]" value="delete" id="ability_delete" {{ is_array(old('abilities')) && in_array('delete', old('abilities')) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="ability_delete">
                                            {{ __('Delete') }}
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">{{ __('Leave unchecked to grant full access.') }}</small>
                                </div>

                                <div class="form-group">
                                    <label for="expiration">{{ __('Expiration (Days)') }}</label>
                                    <input type="number" class="form-control @error('expiration') is-invalid @enderror" id="expiration" name="expiration" value="{{ old('expiration', App\Models\Setting::get('api_token_expiration', 0)) }}" min="0">
                                    @error('expiration')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">{{ __('Number of days before token expires. Use 0 for no expiration.') }}</small>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus-circle mr-2"></i> {{ __('Create Token') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="col-md-7">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">{{ __('Active Tokens') }}</h3>
                            <div class="card-tools">
                                <span class="badge badge-primary">{{ $tokens->count() }} {{ __('Total') }}</span>
                            </div>
                        </div>
                        <div class="card-body table-responsive p-0">
                            <table class="table table-hover text-nowrap">
                                <thead>
                                    <tr>
                                        <th>{{ __('Name') }}</th>
                                        <th>{{ __('User') }}</th>
                                        <th>{{ __('Created') }}</th>
                                        <th>{{ __('Last Used') }}</th>
                                        <th>{{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse ($tokens as $token)
                                        <tr>
                                            <td>
                                                <strong>{{ $token->name }}</strong>
                                                @if($token->expires_at)
                                                    @if($token->expires_at->isPast())
                                                        <span class="badge badge-danger ml-1">{{ __('Expired') }}</span>
                                                    @else
                                                        <span class="badge badge-warning ml-1">{{ __('Expires') }} {{ $token->expires_at->diffForHumans() }}</span>
                                                    @endif
                                                @endif
                                            </td>
                                            <td>{{ $token->tokenable->name }}</td>
                                            <td>{{ $token->created_at->format('M d, Y') }}</td>
                                            <td>{{ $token->last_used_at ? $token->last_used_at->format('M d, Y H:i') : __('Never') }}</td>
                                            <td>
                                                <form action="{{ route('admin.settings.api-tokens.destroy', $token->id) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('{{ __('Are you sure you want to revoke this token?') }}')">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <div class="text-center py-5">
                                                    <i class="fas fa-key fa-3x text-muted mb-3"></i>
                                                    <h5>{{ __('No API tokens found') }}</h5>
                                                    <p class="text-muted">{{ __('Create a new token to get started.') }}</p>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <script>
        function copyToken() {
            var tokenInput = document.getElementById('tokenInput');
            tokenInput.select();
            document.execCommand('copy');
            alert('Token copied to clipboard!');
        }
    </script>
@endsection
