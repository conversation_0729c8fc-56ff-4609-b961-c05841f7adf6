@extends('components.layouts.admin')

@section('title', __('API Settings'))

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('API Settings') }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.settings.index') }}">Settings</a></li>
                        <li class="breadcrumb-item active">API Settings</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">{{ __('API Settings') }}</h3>
                            <div class="card-tools">
                                <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-arrow-left mr-2"></i>{{ __('Back to Settings') }}
                                </a>
                            </div>
                        </div>
                        <form action="{{ route('admin.settings.api.update') }}" method="POST">
                            @csrf
                            @method('PUT')
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-12 mb-3">
                                        <div class="alert alert-info" role="alert">
                                            <i class="fas fa-info-circle mr-2"></i>
                                            {{ __('Configure API settings to control access and security for your API endpoints.') }}
                                        </div>
                                    </div>
                                </div>

                                    @foreach($apiSettings as $setting)
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="{{ $setting->key }}">
                                                    @if($setting->key == 'allowed_api_ips')
                                                        <i class="fas fa-shield-alt mr-2 text-primary"></i>
                                                    @elseif($setting->key == 'api_token_expiration')
                                                        <i class="fas fa-clock mr-2 text-primary"></i>
                                                    @elseif($setting->key == 'api_rate_limit')
                                                        <i class="fas fa-tachometer-alt mr-2 text-primary"></i>
                                                    @endif
                                                    {{ $setting->label }}
                                                </label>
                                                @if($setting->type == 'number')
                                                    <input
                                                        type="number"
                                                        class="form-control @error($setting->key) is-invalid @enderror"
                                                        id="{{ $setting->key }}"
                                                        name="{{ $setting->key }}"
                                                        value="{{ old($setting->key, $setting->value) }}"
                                                        min="0"
                                                    >
                                                @else
                                                    <input
                                                        type="text"
                                                        class="form-control @error($setting->key) is-invalid @enderror"
                                                        id="{{ $setting->key }}"
                                                        name="{{ $setting->key }}"
                                                        value="{{ old($setting->key, $setting->value) }}"
                                                        placeholder="{{ $setting->description }}"
                                                    >
                                                @endif
                                                @error($setting->key)
                                                    <div class="invalid-feedback">
                                                        {{ $message }}
                                                    </div>
                                                @enderror
                                                <small class="form-text text-muted">{{ $setting->description }}</small>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save mr-2"></i> {{ __('Save Changes') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">{{ __('API Documentation') }}</h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info" role="alert">
                                <i class="fas fa-book mr-2"></i>
                                {{ __('API documentation is available to help you integrate with our API endpoints.') }}
                            </div>

                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="font-weight-bold mb-1">{{ __('API Documentation') }}</h6>
                                    <p class="text-muted mb-0">{{ __('View the API documentation to learn how to use the API endpoints.') }}</p>
                                </div>
                                <div>
                                    <a href="{{ route('admin.settings.api.documentation') }}" class="btn btn-outline-primary mr-2">
                                        <i class="fas fa-book mr-1"></i> {{ __('View Documentation') }}
                                    </a>
                                    <a href="{{ route('admin.settings.api-tokens') }}" class="btn btn-outline-primary">
                                        <i class="fas fa-key mr-1"></i> {{ __('Manage API Tokens') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
