@extends('components.layouts.admin')

@section('title', 'Landing Pages')

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Landing Pages</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Landing Pages</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Small boxes (Stat box) -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>{{ $landingPages->total() }}</h3>
                            <p>{{ __('Total Pages') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <a href="{{ route('admin.landing-pages.index') }}" class="small-box-footer">
                            {{ __('View All') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>{{ $landingPages->where('is_active', true)->count() }}</h3>
                            <p>{{ __('Published') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <a href="{{ route('admin.landing-pages.index', ['status' => 'active']) }}" class="small-box-footer">
                            {{ __('View Published') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>{{ $landingPages->where('has_quiz', true)->count() }}</h3>
                            <p>{{ __('With Quiz') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <a href="{{ route('admin.landing-pages.index', ['quiz' => 'enabled']) }}" class="small-box-footer">
                            {{ __('View Quiz Pages') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>{{ $landingPages->sum('leads_count') }}</h3>
                            <p>{{ __('Total Leads') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <a href="{{ route('admin.leads.index') }}" class="small-box-footer">
                            {{ __('View Leads') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>
            <!-- /.row -->

            <!-- Filters and Search -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('Filter Landing Pages') }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.landing-pages.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus mr-2"></i>
                            {{ __('New Landing Page') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.landing-pages.index') }}" method="GET" class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="search">{{ __('Search') }}</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    </div>
                                    <input type="text" class="form-control" id="search" name="search" placeholder="Search by title or description" value="{{ request('search') }}">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="status">{{ __('Status') }}</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="">{{ __('All Statuses') }}</option>
                                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>{{ __('Published') }}</option>
                                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>{{ __('Draft') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="quiz">{{ __('Quiz') }}</label>
                                <select class="form-control" id="quiz" name="quiz">
                                    <option value="">{{ __('All Pages') }}</option>
                                    <option value="enabled" {{ request('quiz') == 'enabled' ? 'selected' : '' }}>{{ __('With Quiz') }}</option>
                                    <option value="disabled" {{ request('quiz') == 'disabled' ? 'selected' : '' }}>{{ __('Without Quiz') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-filter mr-2"></i>{{ __('Filter') }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Landing Pages Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('All Landing Pages') }}</h3>
                    <div class="card-tools">
                        <span class="badge badge-primary">{{ $landingPages->total() }} {{ __('Total') }}</span>
                    </div>
                </div>
                <!-- /.card-header -->
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th>{{ __('Title') }}</th>
                                <th>{{ __('Status') }}</th>
                                <th>{{ __('Leads') }}</th>
                                <th>{{ __('Quiz') }}</th>
                                <th>{{ __('Created') }}</th>
                                <th>{{ __('Actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($landingPages as $landingPage)
                                <tr>
                                    <td>
                                        <strong>{{ $landingPage->title }}</strong>
                                        @if($landingPage->meta_description)
                                            <br><small class="text-muted">{{ Str::limit($landingPage->meta_description, 60) }}</small>
                                        @endif
                                        <br><code class="small">/landing/{{ $landingPage->slug }}</code>
                                    </td>
                                    <td>
                                        <span class="badge {{ $landingPage->is_active ? 'badge-success' : 'badge-secondary' }}">
                                            {{ $landingPage->is_active ? 'Published' : 'Draft' }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="font-weight-bold text-primary">{{ $landingPage->leads_count }}</span>
                                        <small class="text-muted"> {{ $landingPage->leads_count == 1 ? 'lead' : 'leads' }}</small>
                                    </td>
                                    <td>
                                        @if($landingPage->has_quiz)
                                            <span class="badge badge-info">{{ __('Enabled') }}</span>
                                        @else
                                            <span class="text-muted">{{ __('No Quiz') }}</span>
                                        @endif
                                    </td>
                                    <td>{{ $landingPage->created_at->format('M d, Y') }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('admin.landing-pages.show', $landingPage) }}" class="btn btn-sm btn-info" data-toggle="tooltip" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.landing-pages.edit', $landingPage) }}" class="btn btn-sm btn-warning" data-toggle="tooltip" title="Edit">
                                                <i class="fas fa-pencil-alt"></i>
                                            </a>
                                            @if($landingPage->has_quiz)
                                                <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-sm btn-success" data-toggle="tooltip" title="Manage Quiz">
                                                    <i class="fas fa-question-circle"></i>
                                                </a>
                                            @endif
                                            <a href="{{ route('landing-pages.show', $landingPage) }}" target="_blank" class="btn btn-sm btn-secondary" data-toggle="tooltip" title="Preview">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                            <form action="{{ route('admin.landing-pages.destroy', $landingPage) }}" method="POST" class="d-inline" onsubmit="return confirm('{{ __('Are you sure you want to delete this landing page?') }}')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" data-toggle="tooltip" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-center py-5">
                                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                            <h5>{{ __('No landing pages found') }}</h5>
                                            <p class="text-muted">{{ __('No landing pages match your criteria') }}</p>
                                            <a href="{{ route('admin.landing-pages.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus mr-2"></i>{{ __('Create Landing Page') }}
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                <!-- /.card-body -->
                @if($landingPages->hasPages())
                    <div class="card-footer clearfix">
                        {{ $landingPages->links() }}
                    </div>
                @endif
            </div>
            <!-- /.card -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
