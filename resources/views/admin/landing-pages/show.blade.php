@extends('components.layouts.admin')

@section('title', $landingPage->title)

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('Landing Page Details') }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.landing-pages.index') }}">Landing Pages</a></li>
                        <li class="breadcrumb-item active">View</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- <PERSON> Header Card -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ $landingPage->title }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('landing-pages.show', $landingPage) }}" target="_blank" class="btn btn-info btn-sm">
                            <i class="fas fa-external-link-alt mr-2"></i>{{ __('View Live') }}
                        </a>
                        <a href="{{ route('admin.landing-pages.edit', $landingPage) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit mr-2"></i>{{ __('Edit') }}
                        </a>
                        <a href="{{ route('admin.landing-pages.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-2"></i>{{ __('Back') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>{{ __('Status') }}:</strong>
                            <span class="badge badge-{{ $landingPage->is_active ? 'success' : 'secondary' }} ml-2">
                                <i class="fas fa-{{ $landingPage->is_active ? 'check-circle' : 'pause-circle' }} mr-1"></i>
                                {{ $landingPage->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                        <div class="col-md-6">
                            <strong>{{ __('Quiz') }}:</strong>
                            <span class="badge badge-{{ $landingPage->has_quiz ? 'info' : 'secondary' }} ml-2">
                                <i class="fas fa-question-circle mr-1"></i>
                                {{ $landingPage->has_quiz ? 'Enabled' : 'Disabled' }}
                            </span>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <strong>{{ __('Created') }}:</strong> {{ $landingPage->created_at->format('M j, Y g:i A') }}
                        </div>
                        <div class="col-md-6">
                            <strong>{{ __('Last Updated') }}:</strong> {{ $landingPage->updated_at->format('M j, Y g:i A') }}
                        </div>
                    </div>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                    <i class="icon fas fa-check"></i> {{ session('success') }}
                </div>
            @endif

            <!-- Statistics Row -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <!-- small box -->
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>{{ $stats['total_leads'] }}</h3>
                            <p>Total Leads</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <a href="{{ route('admin.leads.index', ['landing_page_id' => $landingPage->id]) }}" class="small-box-footer">
                            More info <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <!-- small box -->
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>{{ $stats['leads_today'] }}</h3>
                            <p>Today</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <a href="{{ route('admin.leads.index', ['landing_page_id' => $landingPage->id]) }}" class="small-box-footer">
                            More info <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <!-- small box -->
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>{{ $stats['leads_this_week'] }}</h3>
                            <p>This Week</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-calendar-week"></i>
                        </div>
                        <a href="{{ route('admin.leads.index', ['landing_page_id' => $landingPage->id]) }}" class="small-box-footer">
                            More info <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <!-- small box -->
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>{{ $stats['avg_quiz_score'] ? number_format($stats['avg_quiz_score'], 1) : 'N/A' }}</h3>
                            <p>Avg Quiz Score</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <a href="{{ route('admin.leads.index', ['landing_page_id' => $landingPage->id]) }}" class="small-box-footer">
                            More info <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main Content Row -->
            <div class="row">
                <div class="col-lg-8">
                    <!-- Page Details -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-info-circle mr-2"></i>Page Details
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <dl class="row">
                                        <dt class="col-sm-4">URL Slug:</dt>
                                        <dd class="col-sm-8">
                                            <code>/landing/{{ $landingPage->slug }}</code>
                                        </dd>
                                        <dt class="col-sm-4">Meta Title:</dt>
                                        <dd class="col-sm-8">{{ $landingPage->meta_title ?: 'Using page title' }}</dd>
                                        @if($landingPage->has_quiz)
                                            <dt class="col-sm-4">Quiz:</dt>
                                            <dd class="col-sm-8">
                                                <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-cog mr-1"></i>Manage Quiz
                                                </a>
                                            </dd>
                                        @endif
                                    </dl>
                                </div>
                                <div class="col-md-6">
                                    <dl class="row">
                                        <dt class="col-sm-4">Created:</dt>
                                        <dd class="col-sm-8">{{ $landingPage->created_at->format('M j, Y g:i A') }}</dd>
                                        <dt class="col-sm-4">Updated:</dt>
                                        <dd class="col-sm-8">{{ $landingPage->updated_at->format('M j, Y g:i A') }}</dd>
                                    </dl>
                                </div>
                            </div>
                            @if($landingPage->meta_description)
                                <div class="mt-3">
                                    <strong>Meta Description:</strong>
                                    <p class="mt-2 p-3 bg-light rounded">{{ $landingPage->meta_description }}</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Recent Leads -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-users mr-2"></i>Recent Leads
                            </h3>
                            <div class="card-tools">
                                <a href="{{ route('admin.leads.index', ['landing_page_id' => $landingPage->id]) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-arrow-right mr-1"></i>View All
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            @if($landingPage->leads->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Company</th>
                                                <th>Quiz Score</th>
                                                <th>Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($landingPage->leads as $lead)
                                                <tr>
                                                    <td>{{ $lead->name }}</td>
                                                    <td>{{ $lead->email }}</td>
                                                    <td>{{ $lead->company_name }}</td>
                                                    <td>
                                                        @if($lead->quiz_score !== null)
                                                            <span class="badge badge-info">{{ $lead->quiz_score }} pts</span>
                                                        @else
                                                            <span class="text-muted">N/A</span>
                                                        @endif
                                                    </td>
                                                    <td>{{ $lead->created_at->format('M j, Y') }}</td>
                                                    <td>
                                                        <a href="{{ route('admin.leads.show', $lead) }}" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No leads yet</h5>
                                    <p class="text-muted">Leads will appear here when visitors complete the assessment.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-bolt mr-2"></i>Quick Actions
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="btn-group-vertical btn-block">
                                <a href="{{ route('admin.landing-pages.edit', $landingPage) }}" class="btn btn-primary mb-2">
                                    <i class="fas fa-edit mr-2"></i>Edit Page
                                </a>
                                @if($landingPage->has_quiz)
                                    <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-info mb-2">
                                        <i class="fas fa-question-circle mr-2"></i>Manage Quiz
                                    </a>
                                @endif
                                <a href="{{ route('admin.leads.index', ['landing_page_id' => $landingPage->id]) }}" class="btn btn-success mb-2">
                                    <i class="fas fa-users mr-2"></i>View All Leads
                                </a>
                                <a href="{{ route('landing-pages.show', $landingPage) }}" target="_blank" class="btn btn-secondary">
                                    <i class="fas fa-external-link-alt mr-2"></i>Preview Page
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Quiz Information -->
                    @if($landingPage->quiz)
                        <div class="card mt-3">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-question-circle mr-2"></i>Quiz Information
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="info-box bg-info">
                                            <span class="info-box-icon"><i class="fas fa-question"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">Questions</span>
                                                <span class="info-box-number">{{ $landingPage->quiz->questions->count() }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="info-box bg-warning">
                                            <span class="info-box-icon"><i class="fas fa-star"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">Max Score</span>
                                                <span class="info-box-number">{{ $landingPage->quiz->getTotalPossibleScore() }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <dl class="row mt-3">
                                    <dt class="col-sm-4">Quiz Title:</dt>
                                    <dd class="col-sm-8">{{ $landingPage->quiz->title }}</dd>
                                    <dt class="col-sm-4">Status:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge badge-{{ $landingPage->quiz->is_active ? 'success' : 'secondary' }}">
                                            {{ $landingPage->quiz->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </dd>
                                </dl>

                                @if($landingPage->quiz->description)
                                    <div class="mt-3">
                                        <strong>Description:</strong>
                                        <p class="mt-2 p-3 bg-light rounded">{{ $landingPage->quiz->description }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
