@extends('components.layouts.admin')

@section('title', 'Create Landing Page')

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Create Landing Page</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.landing-pages.index') }}">Landing Pages</a></li>
                        <li class="breadcrumb-item active">Create</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1">Create New Landing Page</h2>
                            <p class="text-muted mb-0">Design and configure a new landing page for lead generation</p>
                        </div>
                        <a href="{{ route('admin.landing-pages.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Landing Pages
                        </a>
                    </div>
                </div>
            </div>

            <!-- Form -->
            <form method="POST" action="{{ route('admin.landing-pages.store') }}" id="landing-page-form">
                @csrf

                <div class="row">
                    <!-- Main Content Column -->
                    <div class="col-lg-8">
                        <!-- Basic Information Card -->
                        <div class="card card-primary card-outline">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-edit mr-2"></i>Basic Information
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="title" class="form-label">
                                        Page Title <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control @error('title') is-invalid @enderror"
                                           id="title"
                                           name="title"
                                           value="{{ old('title') }}"
                                           placeholder="Enter a compelling title for your landing page"
                                           required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">This will be displayed as the main heading on your landing page</small>
                                </div>

                                <div class="form-group">
                                    <label for="slug" class="form-label">URL Slug</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">{{ url('/landing/') }}/</span>
                                        </div>
                                        <input type="text"
                                               class="form-control @error('slug') is-invalid @enderror"
                                               id="slug"
                                               name="slug"
                                               value="{{ old('slug') }}"
                                               placeholder="auto-generated-from-title">
                                        @error('slug')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        Leave blank to auto-generate from title. Only letters, numbers, and hyphens allowed.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Content Editor Card -->
                        <div class="card card-primary card-outline">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-file-alt mr-2"></i>Page Content
                                </h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="content" class="form-label">
                                        Landing Page Content <span class="text-danger">*</span>
                                    </label>
                                    <textarea id="content"
                                              name="content"
                                              class="form-control @error('content') is-invalid @enderror"
                                              rows="20">{{ old('content') }}</textarea>
                                    @error('content')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        <i class="fas fa-lightbulb mr-1"></i>
                                        Use the rich text editor to create compelling content. Include headlines, benefits, testimonials, and clear calls-to-action.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar Column -->
                    <div class="col-lg-4">
                        <!-- Publish Settings Card -->
                        <div class="card card-success card-outline">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-cog mr-2"></i>Publish Settings
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox"
                                               class="custom-control-input"
                                               id="is_active"
                                               name="is_active"
                                               value="1"
                                               {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_active">
                                            <strong>Publish Landing Page</strong>
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Make this landing page publicly accessible to visitors</small>
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox"
                                               class="custom-control-input"
                                               id="has_quiz"
                                               name="has_quiz"
                                               value="1"
                                               {{ old('has_quiz', true) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="has_quiz">
                                            <strong>Enable Assessment Quiz</strong>
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Include a quiz button to assess visitor needs before consultation</small>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    <strong>Tip:</strong> Enable the quiz to qualify leads and provide personalized recommendations.
                                </div>
                            </div>
                        </div>

                        <!-- SEO Settings Card -->
                        <div class="card card-warning card-outline">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-search mr-2"></i>SEO Settings
                                </h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="meta_title" class="form-label">Meta Title</label>
                                    <input type="text"
                                           class="form-control @error('meta_title') is-invalid @enderror"
                                           id="meta_title"
                                           name="meta_title"
                                           value="{{ old('meta_title') }}"
                                           maxlength="60"
                                           placeholder="SEO-optimized title for search engines">
                                    @error('meta_title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        <span id="meta-title-count">0</span>/60 characters. Leave blank to use page title.
                                    </small>
                                </div>

                                <div class="form-group">
                                    <label for="meta_description" class="form-label">Meta Description</label>
                                    <textarea class="form-control @error('meta_description') is-invalid @enderror"
                                              id="meta_description"
                                              name="meta_description"
                                              rows="3"
                                              maxlength="160"
                                              placeholder="Brief description for search engine results">{{ old('meta_description') }}</textarea>
                                    @error('meta_description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        <span id="meta-desc-count">0</span>/160 characters. Recommended: 150-160 characters.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons Card -->
                        <div class="card">
                            <div class="card-body">
                                <button type="submit" class="btn btn-highlight btn-lg btn-block">
                                    <i class="fas fa-save mr-2"></i>Create Landing Page
                                </button>
                                <button type="button" class="btn btn-secondary btn-block mt-2" onclick="window.history.back()">
                                    <i class="fas fa-times mr-2"></i>Cancel
                                </button>
                            </div>
                        </div>

                        <!-- Help Card -->
                        <div class="card card-info card-outline">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-question-circle mr-2"></i>Need Help?
                                </h3>
                            </div>
                            <div class="card-body">
                                <p class="text-sm">
                                    <strong>Landing Page Tips:</strong>
                                </p>
                                <ul class="text-sm">
                                    <li>Use compelling headlines that address pain points</li>
                                    <li>Include clear benefits and value propositions</li>
                                    <li>Add social proof and testimonials</li>
                                    <li>Use strong call-to-action buttons</li>
                                    <li>Keep forms simple and focused</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Include TinyMCE for rich text editing -->
            <script src="https://cdn.tiny.cloud/1/laqmad4amsu52pi0fiou1nb91hscak79b3jsijl5340v0a8s/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
            <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Auto-generate slug from title
                const titleInput = document.getElementById('title');
                const slugInput = document.getElementById('slug');

                titleInput.addEventListener('input', function() {
                    if (!slugInput.value || slugInput.dataset.autoGenerated) {
                        const slug = this.value
                            .toLowerCase()
                            .replace(/[^a-z0-9]+/g, '-')
                            .replace(/^-+|-+$/g, '');
                        slugInput.value = slug;
                        slugInput.dataset.autoGenerated = 'true';
                    }
                });

                slugInput.addEventListener('input', function() {
                    if (this.value) {
                        delete this.dataset.autoGenerated;
                    }
                });

                // Character counters for SEO fields
                const metaTitleInput = document.getElementById('meta_title');
                const metaDescInput = document.getElementById('meta_description');
                const metaTitleCount = document.getElementById('meta-title-count');
                const metaDescCount = document.getElementById('meta-desc-count');

                function updateCharCount(input, counter) {
                    if (input && counter) {
                        counter.textContent = input.value.length;

                        // Update color based on length
                        const maxLength = input.getAttribute('maxlength');
                        const currentLength = input.value.length;
                        const percentage = (currentLength / maxLength) * 100;

                        if (percentage > 90) {
                            counter.className = 'text-danger';
                        } else if (percentage > 75) {
                            counter.className = 'text-warning';
                        } else {
                            counter.className = 'text-success';
                        }
                    }
                }

                if (metaTitleInput && metaTitleCount) {
                    updateCharCount(metaTitleInput, metaTitleCount);
                    metaTitleInput.addEventListener('input', function() {
                        updateCharCount(this, metaTitleCount);
                    });
                }

                if (metaDescInput && metaDescCount) {
                    updateCharCount(metaDescInput, metaDescCount);
                    metaDescInput.addEventListener('input', function() {
                        updateCharCount(this, metaDescCount);
                    });
                }

                // Form validation
                const form = document.getElementById('landing-page-form');
                form.addEventListener('submit', function(e) {
                    const title = titleInput.value.trim();
                    const content = tinymce.get('content').getContent();

                    if (!title) {
                        e.preventDefault();
                        titleInput.focus();
                        toastr.error('Please enter a page title');
                        return false;
                    }

                    if (!content || content.trim() === '') {
                        e.preventDefault();
                        tinymce.get('content').focus();
                        toastr.error('Please enter page content');
                        return false;
                    }

                    // Show loading state
                    const submitBtn = form.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating...';
                    submitBtn.disabled = true;

                    // Re-enable button after 10 seconds as fallback
                    setTimeout(function() {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 10000);
                });

                // Initialize TinyMCE
                tinymce.init({
                    selector: '#content',
                    height: 500,
                    menubar: false,
                    plugins: [
                        'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                        'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                        'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste'
                    ],
                    toolbar: 'undo redo | blocks | ' +
                        'bold italic underline strikethrough | forecolor backcolor | ' +
                        'alignleft aligncenter alignright alignjustify | ' +
                        'bullist numlist outdent indent | ' +
                        'link image media table | ' +
                        'code preview fullscreen | help',
                    content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; line-height: 1.6; }',
                    paste_as_text: false,
                    paste_auto_cleanup_on_paste: true,
                    paste_remove_styles: false,
                    paste_remove_styles_if_webkit: false,
                    paste_strip_class_attributes: 'none',
                    setup: function(editor) {
                        editor.on('change', function() {
                            editor.save();
                        });
                    }
                });

                // Auto-save functionality (optional)
                let autoSaveTimer;
                function autoSave() {
                    clearTimeout(autoSaveTimer);
                    autoSaveTimer = setTimeout(function() {
                        const formData = new FormData(form);
                        // You can implement auto-save to localStorage here if needed
                        console.log('Auto-save triggered');
                    }, 30000); // Auto-save every 30 seconds
                }

                // Trigger auto-save on content changes
                titleInput.addEventListener('input', autoSave);
                slugInput.addEventListener('input', autoSave);
            });
            </script>
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
