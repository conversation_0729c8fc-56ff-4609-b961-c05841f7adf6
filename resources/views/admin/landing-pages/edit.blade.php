@extends('components.layouts.admin')

@section('title', 'Edit Landing Page')

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Edit Landing Page</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.landing-pages.index') }}">Landing Pages</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.landing-pages.show', $landingPage) }}">{{ $landingPage->title }}</a></li>
                        <li class="breadcrumb-item active">Edit</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">


            @if(session('success'))
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                    <i class="icon fas fa-check"></i> {{ session('success') }}
                </div>
            @endif

            <!-- Form -->
            <form method="POST" action="{{ route('admin.landing-pages.update', $landingPage) }}" id="landing-page-form">
                @csrf
                @method('PUT')

                <div class="row">
                    <!-- Main Content Column -->
                    <div class="col-lg-8">
                        <!-- Basic Information Card -->
                        <div class="card card-primary card-outline">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-edit mr-2"></i>Basic Information
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="title" class="form-label">
                                        Page Title <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control @error('title') is-invalid @enderror"
                                           id="title"
                                           name="title"
                                           value="{{ old('title', $landingPage->title) }}"
                                           placeholder="Enter a compelling title for your landing page"
                                           required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">This will be displayed as the main heading on your landing page</small>
                                </div>

                                <div class="form-group">
                                    <label for="slug" class="form-label">URL Slug</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">{{ url('/landing/') }}/</span>
                                        </div>
                                        <input type="text"
                                               class="form-control @error('slug') is-invalid @enderror"
                                               id="slug"
                                               name="slug"
                                               value="{{ old('slug', $landingPage->slug) }}"
                                               placeholder="auto-generated-from-title">
                                        @error('slug')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-link mr-1"></i>
                                        Current URL: <a href="{{ route('landing-pages.show', $landingPage) }}" target="_blank" class="text-primary">{{ route('landing-pages.show', $landingPage) }}</a>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Content Editor Card -->
                        <div class="card card-primary card-outline">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-file-alt mr-2"></i>Page Content
                                </h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="content" class="form-label">
                                        Landing Page Content <span class="text-danger">*</span>
                                    </label>
                                    <textarea id="content"
                                              name="content"
                                              class="form-control @error('content') is-invalid @enderror"
                                              rows="20">{{ old('content', $landingPage->content) }}</textarea>
                                    @error('content')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        <i class="fas fa-lightbulb mr-1"></i>
                                        Use the rich text editor to create compelling content. Include headlines, benefits, testimonials, and clear calls-to-action.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Statistics Card -->
                        <div class="card card-info card-outline">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-chart-line mr-2"></i>Performance Overview
                                </h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="info-box bg-info">
                                            <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">Total Leads</span>
                                                <span class="info-box-number">{{ $landingPage->leads()->count() }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="info-box bg-success">
                                            <span class="info-box-icon"><i class="fas fa-calendar-day"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">Today</span>
                                                <span class="info-box-number">{{ $landingPage->leads()->whereDate('created_at', today())->count() }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="info-box bg-warning">
                                            <span class="info-box-icon"><i class="fas fa-calendar-week"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">This Week</span>
                                                <span class="info-box-number">{{ $landingPage->leads()->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count() }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="info-box bg-danger">
                                            <span class="info-box-icon"><i class="fas fa-star"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">Avg Score</span>
                                                <span class="info-box-number">{{ number_format($landingPage->leads()->whereNotNull('quiz_score')->avg('quiz_score') ?? 0, 1) }}%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <p class="text-sm text-muted mb-0">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Last updated: {{ $landingPage->updated_at->diffForHumans() }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar Column -->
                    <div class="col-lg-4">
                        <!-- Publish Settings Card -->
                        <div class="card card-success card-outline">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-cog mr-2"></i>Publish Settings
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox"
                                               class="custom-control-input"
                                               id="is_active"
                                               name="is_active"
                                               value="1"
                                               {{ old('is_active', $landingPage->is_active) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_active">
                                            <strong>Publish Landing Page</strong>
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Make this landing page publicly accessible to visitors</small>
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox"
                                               class="custom-control-input"
                                               id="has_quiz"
                                               name="has_quiz"
                                               value="1"
                                               {{ old('has_quiz', $landingPage->has_quiz) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="has_quiz">
                                            <strong>Enable Assessment Quiz</strong>
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Include a quiz button to assess visitor needs before consultation</small>
                                </div>

                                <div class="alert alert-{{ $landingPage->is_active ? 'success' : 'warning' }}">
                                    <i class="fas fa-{{ $landingPage->is_active ? 'check-circle' : 'exclamation-triangle' }} mr-2"></i>
                                    <strong>Status:</strong> This page is currently {{ $landingPage->is_active ? 'published and visible to visitors' : 'in draft mode and not visible to visitors' }}.
                                </div>
                            </div>
                        </div>

                        <!-- SEO Settings Card -->
                        <div class="card card-warning card-outline">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-search mr-2"></i>SEO Settings
                                </h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="meta_title" class="form-label">Meta Title</label>
                                    <input type="text"
                                           class="form-control @error('meta_title') is-invalid @enderror"
                                           id="meta_title"
                                           name="meta_title"
                                           value="{{ old('meta_title', $landingPage->meta_title) }}"
                                           maxlength="60"
                                           placeholder="SEO-optimized title for search engines">
                                    @error('meta_title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        <span id="meta-title-count">{{ strlen($landingPage->meta_title ?? '') }}</span>/60 characters. Leave blank to use page title.
                                    </small>
                                </div>

                                <div class="form-group">
                                    <label for="meta_description" class="form-label">Meta Description</label>
                                    <textarea class="form-control @error('meta_description') is-invalid @enderror"
                                              id="meta_description"
                                              name="meta_description"
                                              rows="3"
                                              maxlength="160"
                                              placeholder="Brief description for search engine results">{{ old('meta_description', $landingPage->meta_description) }}</textarea>
                                    @error('meta_description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        <span id="meta-desc-count">{{ strlen($landingPage->meta_description ?? '') }}</span>/160 characters. Recommended: 150-160 characters.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons Card -->
                        <div class="card">
                            <div class="card-body">
                                <button type="submit" class="btn btn-highlight btn-lg btn-block">
                                    <i class="fas fa-save mr-2"></i>Update Landing Page
                                </button>
                                <div class="btn-group btn-block mt-2">
                                    <a href="{{ route('admin.landing-pages.show', $landingPage) }}" class="btn btn-info">
                                        <i class="fas fa-eye mr-2"></i>View Details
                                    </a>
                                    <a href="{{ route('landing-pages.show', $landingPage) }}" target="_blank" class="btn btn-success">
                                        <i class="fas fa-external-link-alt mr-2"></i>Preview
                                    </a>
                                </div>
                                <button type="button" class="btn btn-secondary btn-block mt-2" onclick="window.history.back()">
                                    <i class="fas fa-times mr-2"></i>Cancel
                                </button>
                            </div>
                        </div>

                        <!-- Page Information Card -->
                        <div class="card card-secondary card-outline">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-info-circle mr-2"></i>Page Information
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <strong>Created:</strong>
                                    </div>
                                    <div class="col-6">
                                        {{ $landingPage->created_at->format('M j, Y') }}
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <strong>Last Updated:</strong>
                                    </div>
                                    <div class="col-6">
                                        {{ $landingPage->updated_at->diffForHumans() }}
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <strong>Total Leads:</strong>
                                    </div>
                                    <div class="col-6">
                                        <span class="badge badge-primary">{{ $landingPage->leads()->count() }}</span>
                                    </div>
                                </div>
                                @if($landingPage->has_quiz && $landingPage->quiz)
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <strong>Quiz Questions:</strong>
                                    </div>
                                    <div class="col-6">
                                        <span class="badge badge-info">{{ $landingPage->quiz->questions()->count() }}</span>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>

                        <!-- Help Card -->
                        <div class="card card-info card-outline">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-question-circle mr-2"></i>Need Help?
                                </h3>
                            </div>
                            <div class="card-body">
                                <p class="text-sm">
                                    <strong>Editing Tips:</strong>
                                </p>
                                <ul class="text-sm">
                                    <li>Test changes by using the Preview button</li>
                                    <li>Monitor performance with the statistics above</li>
                                    <li>Update SEO settings for better search visibility</li>
                                    <li>Use the quiz feature to qualify leads</li>
                                    <li>Keep content fresh and engaging</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </form>

        <!-- Include TinyMCE for rich text editing -->
        <script src="https://cdn.tiny.cloud/1/laqmad4amsu52pi0fiou1nb91hscak79b3jsijl5340v0a8s/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-generate slug from title (optional for edit)
            const titleInput = document.getElementById('title');
            const slugInput = document.getElementById('slug');

            titleInput.addEventListener('input', function() {
                // Only auto-generate if slug is empty or matches the current auto-generated pattern
                if (!slugInput.value || slugInput.dataset.autoGenerated) {
                    const slug = this.value
                        .toLowerCase()
                        .replace(/[^a-z0-9]+/g, '-')
                        .replace(/^-+|-+$/g, '');
                    slugInput.value = slug;
                    slugInput.dataset.autoGenerated = 'true';
                }
            });

            slugInput.addEventListener('input', function() {
                if (this.value) {
                    delete this.dataset.autoGenerated;
                }
            });

            // Character counters for SEO fields
            const metaTitleInput = document.getElementById('meta_title');
            const metaDescInput = document.getElementById('meta_description');
            const metaTitleCount = document.getElementById('meta-title-count');
            const metaDescCount = document.getElementById('meta-desc-count');

            function updateCharCount(input, counter) {
                if (input && counter) {
                    counter.textContent = input.value.length;

                    // Update color based on length
                    const maxLength = input.getAttribute('maxlength');
                    const currentLength = input.value.length;
                    const percentage = (currentLength / maxLength) * 100;

                    if (percentage > 90) {
                        counter.className = 'text-danger';
                    } else if (percentage > 75) {
                        counter.className = 'text-warning';
                    } else {
                        counter.className = 'text-success';
                    }
                }
            }

            if (metaTitleInput && metaTitleCount) {
                updateCharCount(metaTitleInput, metaTitleCount);
                metaTitleInput.addEventListener('input', function() {
                    updateCharCount(this, metaTitleCount);
                });
            }

            if (metaDescInput && metaDescCount) {
                updateCharCount(metaDescInput, metaDescCount);
                metaDescInput.addEventListener('input', function() {
                    updateCharCount(this, metaDescCount);
                });
            }

            // Form validation
            const form = document.getElementById('landing-page-form');
            form.addEventListener('submit', function(e) {
                const title = titleInput.value.trim();
                const content = tinymce.get('content').getContent();

                if (!title) {
                    e.preventDefault();
                    titleInput.focus();
                    toastr.error('Please enter a page title');
                    return false;
                }

                if (!content || content.trim() === '') {
                    e.preventDefault();
                    tinymce.get('content').focus();
                    toastr.error('Please enter page content');
                    return false;
                }

                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Updating...';
                submitBtn.disabled = true;

                // Re-enable button after 10 seconds as fallback
                setTimeout(function() {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 10000);
            });

            // Warn about unsaved changes
            let formChanged = false;
            const formElements = form.querySelectorAll('input, textarea, select');

            formElements.forEach(function(element) {
                element.addEventListener('change', function() {
                    formChanged = true;
                });
            });

            window.addEventListener('beforeunload', function(e) {
                if (formChanged) {
                    e.preventDefault();
                    e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
                    return e.returnValue;
                }
            });

            // Reset form changed flag on successful submit
            form.addEventListener('submit', function() {
                formChanged = false;
            });

            // Initialize TinyMCE
            tinymce.init({
                selector: '#content',
                height: 500,
                menubar: false,
                plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                    'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste'
                ],
                toolbar: 'undo redo | blocks | ' +
                    'bold italic underline strikethrough | forecolor backcolor | ' +
                    'alignleft aligncenter alignright alignjustify | ' +
                    'bullist numlist outdent indent | ' +
                    'link image media table | ' +
                    'code preview fullscreen | help',
                content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; line-height: 1.6; }',
                paste_as_text: false,
                paste_auto_cleanup_on_paste: true,
                paste_remove_styles: false,
                paste_remove_styles_if_webkit: false,
                paste_strip_class_attributes: 'none',
                setup: function(editor) {
                    editor.on('change', function() {
                        editor.save();
                        formChanged = true;
                    });
                }
            });

            // Auto-save functionality (optional)
            let autoSaveTimer;
            function autoSave() {
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(function() {
                    const formData = new FormData(form);
                    // You can implement auto-save to localStorage here if needed
                    console.log('Auto-save triggered');
                }, 30000); // Auto-save every 30 seconds
            }

            // Trigger auto-save on content changes
            titleInput.addEventListener('input', autoSave);
            slugInput.addEventListener('input', autoSave);
        });
        </script>
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
