@extends('components.layouts.admin')

@section('title', __('Manage Consultation Statuses'))

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('Consultation Statuses') }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Consultation Statuses</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Small boxes (Stat box) -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>{{ count($statuses) }}</h3>
                            <p>{{ __('Total Statuses') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <a href="{{ route('admin.consultation-statuses.index') }}" class="small-box-footer">
                            {{ __('View All') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>{{ $statuses->where('is_default', true)->count() }}</h3>
                            <p>{{ __('Default Status') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <a href="{{ route('admin.consultation-statuses.index') }}" class="small-box-footer">
                            {{ __('View Default') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>{{ $statuses->sum(function($status) { return $status->consultations()->count(); }) }}</h3>
                            <p>{{ __('Consultations Using') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <a href="{{ route('admin.consultations.index') }}" class="small-box-footer">
                            {{ __('View Consultations') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>{{ $statuses->where('consultations_count', 0)->count() }}</h3>
                            <p>{{ __('Unused Statuses') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <a href="{{ route('admin.consultation-statuses.index') }}" class="small-box-footer">
                            {{ __('View Unused') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>
            <!-- /.row -->

            <!-- Statuses Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('All Statuses') }}</h3>
                    <div class="card-tools">
                        <span class="badge badge-primary mr-2">{{ count($statuses) }} {{ __('Total') }}</span>
                        <a href="{{ route('admin.consultation-statuses.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus mr-2"></i>
                            {{ __('New Status') }}
                        </a>
                    </div>
                </div>
                <!-- /.card-header -->
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th style="width: 50px;">{{ __('Color') }}</th>
                                <th>{{ __('Name') }}</th>
                                <th>{{ __('Description') }}</th>
                                <th>{{ __('Sort Order') }}</th>
                                <th>{{ __('Default') }}</th>
                                <th>{{ __('Actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($statuses as $status)
                                <tr>
                                    <td>
                                        <div class="color-swatch" style="background-color: {{ $status->color }}; width: 30px; height: 30px; border-radius: 4px;"></div>
                                    </td>
                                    <td>
                                        <span class="badge" style="background-color: {{ $status->color }};">{{ $status->name }}</span>
                                    </td>
                                    <td>{{ Str::limit($status->description, 50) }}</td>
                                    <td>{{ $status->sort_order }}</td>
                                    <td>
                                        @if ($status->is_default)
                                            <span class="badge badge-success">{{ __('Default') }}</span>
                                        @else
                                            <form action="{{ route('admin.consultation-statuses.set-default', $status) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit" class="btn btn-sm btn-secondary">
                                                    {{ __('Set as Default') }}
                                                </button>
                                            </form>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('admin.consultation-statuses.edit', $status) }}" class="btn btn-sm btn-warning" data-toggle="tooltip" title="Edit">
                                                <i class="fas fa-pencil-alt"></i>
                                            </a>
                                            @if (!$status->is_default && $status->consultations()->count() === 0)
                                                <form action="{{ route('admin.consultation-statuses.destroy', $status) }}" method="POST" class="d-inline" onsubmit="return confirm('{{ __('Are you sure you want to delete this status?') }}')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" data-toggle="tooltip" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-center py-5">
                                            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                            <h5>{{ __('No statuses found') }}</h5>
                                            <p class="text-muted">{{ __('Create your first consultation status') }}</p>
                                            <a href="{{ route('admin.consultation-statuses.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus mr-2"></i>
                                                {{ __('New Status') }}
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                <!-- /.card-body -->
            </div>
            <!-- /.card -->

            <!-- Info Card -->
            <div class="card card-info">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        {{ __('About Consultation Statuses') }}
                    </h3>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ __('Consultation statuses help you track the progress of consultation requests. The default status is automatically assigned to new consultations. You cannot delete a status that is being used by consultations.') }}</p>
                </div>
            </div>
            <!-- /.card -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
