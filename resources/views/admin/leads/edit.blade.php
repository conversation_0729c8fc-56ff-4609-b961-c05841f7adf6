@extends('components.layouts.admin')

@section('title', 'Edit Lead - ' . $lead->name)

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('Edit Lead') }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Leads</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Edit Lead</h1>
    <div>
        <a href="{{ route('admin.leads.show', $lead) }}" class="btn btn-outline-primary me-2">
            <i class="fas fa-eye me-2"></i>View
        </a>
        <a href="{{ route('admin.leads.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back
        </a>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<form method="POST" action="{{ route('admin.leads.update', $lead) }}">
    @csrf
    @method('PUT')
    
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Lead Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Full Name *</label>
                            <input type="text" 
                                   class="form-control @error('name') is-invalid @enderror" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name', $lead->name) }}" 
                                   required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" 
                                   class="form-control @error('email') is-invalid @enderror" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old('email', $lead->email) }}" 
                                   required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="company_name" class="form-label">Company Name *</label>
                            <input type="text" 
                                   class="form-control @error('company_name') is-invalid @enderror" 
                                   id="company_name" 
                                   name="company_name" 
                                   value="{{ old('company_name', $lead->company_name) }}" 
                                   required>
                            @error('company_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone_number" class="form-label">Phone Number</label>
                            <input type="tel" 
                                   class="form-control @error('phone_number') is-invalid @enderror" 
                                   id="phone_number" 
                                   name="phone_number" 
                                   value="{{ old('phone_number', $lead->phone_number) }}">
                            @error('phone_number')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="availability" class="form-label">Availability</label>
                        <textarea class="form-control @error('availability') is-invalid @enderror" 
                                  id="availability" 
                                  name="availability" 
                                  rows="4"
                                  placeholder="Lead's availability for consultation calls...">{{ old('availability', $lead->availability) }}</textarea>
                        @error('availability')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Lead Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="landing_page_id" class="form-label">Landing Page *</label>
                        <select class="form-select @error('landing_page_id') is-invalid @enderror" 
                                id="landing_page_id" 
                                name="landing_page_id" 
                                required>
                            @foreach($landingPages as $landingPage)
                                <option value="{{ $landingPage->id }}" 
                                        {{ old('landing_page_id', $lead->landing_page_id) == $landingPage->id ? 'selected' : '' }}>
                                    {{ $landingPage->title }}
                                </option>
                            @endforeach
                        </select>
                        @error('landing_page_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="quiz_score" class="form-label">Quiz Score</label>
                        <input type="number" 
                               class="form-control @error('quiz_score') is-invalid @enderror" 
                               id="quiz_score" 
                               name="quiz_score" 
                               value="{{ old('quiz_score', $lead->quiz_score) }}" 
                               min="0">
                        @error('quiz_score')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Leave blank if no quiz was completed</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="pdf_sent" 
                                   name="pdf_sent" 
                                   value="1" 
                                   {{ old('pdf_sent', $lead->pdf_sent) ? 'checked' : '' }}>
                            <label class="form-check-label" for="pdf_sent">
                                PDF Report Sent
                            </label>
                        </div>
                        <small class="form-text text-muted">Mark as sent when PDF report has been delivered</small>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Lead Statistics</h5>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-6">Created:</dt>
                        <dd class="col-sm-6">{{ $lead->created_at->format('M j, Y') }}</dd>
                        
                        <dt class="col-sm-6">Updated:</dt>
                        <dd class="col-sm-6">{{ $lead->updated_at->format('M j, Y') }}</dd>
                        
                        <dt class="col-sm-6">Source:</dt>
                        <dd class="col-sm-6">{{ $lead->landingPage->title }}</dd>
                    </dl>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <button type="submit" class="btn btn-highlight w-100">
                        <i class="fas fa-save me-2"></i>Update Lead
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

@if($lead->quiz_responses && count($lead->quiz_responses) > 0)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Quiz Responses (Read Only)</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Quiz responses cannot be edited. They are preserved as submitted by the lead.
                    </div>
                    
                    @if($lead->landingPage->quiz)
                        @foreach($lead->landingPage->quiz->questions as $question)
                            @php
                                $response = $lead->quiz_responses[$question->id] ?? null;
                            @endphp
                            
                            <div class="mb-3 pb-3 border-bottom">
                                <h6 class="fw-bold">{{ $question->question }}</h6>
                                
                                @if($response)
                                    @if($question->type === 'multiple_choice' && is_array($response))
                                        <ul class="list-unstyled mb-0">
                                            @foreach($response as $selectedOption)
                                                @php
                                                    $options = $question->getFormattedOptions();
                                                    $optionText = $options[$selectedOption]['text'] ?? $selectedOption;
                                                @endphp
                                                <li><i class="fas fa-check text-success me-2"></i>{{ $optionText }}</li>
                                            @endforeach
                                        </ul>
                                    
                                    @elseif($question->type === 'single_choice')
                                        @php
                                            $options = $question->getFormattedOptions();
                                            $optionText = $options[$response]['text'] ?? $response;
                                        @endphp
                                        <p class="mb-0"><i class="fas fa-check text-success me-2"></i>{{ $optionText }}</p>
                                    
                                    @elseif($question->type === 'scale')
                                        <span class="badge bg-primary">{{ $response }}/10</span>
                                    
                                    @elseif($question->type === 'text')
                                        <div class="bg-light p-2 rounded">
                                            {!! nl2br(e($response)) !!}
                                        </div>
                                    @endif
                                @else
                                    <p class="text-muted mb-0"><em>No response provided</em></p>
                                @endif
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>
        </div>
    </div>
@endif
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
