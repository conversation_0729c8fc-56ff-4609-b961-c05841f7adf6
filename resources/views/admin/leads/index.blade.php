@extends('components.layouts.admin')

@section('title', 'Leads')

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('Leads') }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Leads</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Small boxes (Stat box) -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>{{ $leads->total() }}</h3>
                            <p>{{ __('Total Leads') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <a href="{{ route('admin.leads.index') }}" class="small-box-footer">
                            {{ __('View All') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>{{ $leads->where('availability', '!=', null)->count() }}</h3>
                            <p>{{ __('With Availability') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <a href="{{ route('admin.leads.index') }}" class="small-box-footer">
                            {{ __('View Available') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>{{ $leads->where('quiz_score', '!=', null)->count() }}</h3>
                            <p>{{ __('With Quiz Score') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <a href="{{ route('admin.leads.index') }}" class="small-box-footer">
                            {{ __('View Scored') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>{{ $leads->where('pdf_sent', true)->count() }}</h3>
                            <p>{{ __('PDF Reports Sent') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <a href="{{ route('admin.leads.index') }}" class="small-box-footer">
                            {{ __('View Reports') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>
            <!-- /.row -->

            <!-- Filters -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('Filter Leads') }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.leads.export', request()->query()) }}" class="btn btn-success btn-sm">
                            <i class="fas fa-download mr-2"></i>Export CSV
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.leads.index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="landing_page_id">Landing Page</label>
                                    <select class="form-control" id="landing_page_id" name="landing_page_id">
                                        <option value="">All Landing Pages</option>
                                        @foreach($landingPages as $landingPage)
                                            <option value="{{ $landingPage->id }}" {{ request('landing_page_id') == $landingPage->id ? 'selected' : '' }}>
                                                {{ $landingPage->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="date_from">From Date</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="date_to">To Date</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search">Search</label>
                                    <input type="text" class="form-control" id="search" name="search" value="{{ request('search') }}" placeholder="Name, email, or company">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div class="btn-group btn-block">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i>
                                        </button>
                                        <a href="{{ route('admin.leads.index') }}" class="btn btn-secondary">
                                            <i class="fas fa-times"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Leads Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('All Leads') }}</h3>
                    <div class="card-tools">
                        <span class="badge badge-primary">{{ $leads->total() }} {{ __('Total') }}</span>
                    </div>
                </div>
                <!-- /.card-header -->
                <div class="card-body table-responsive p-0">
                    @if($leads->count() > 0)
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th>Lead</th>
                                    <th>Contact Info</th>
                                    <th>Company</th>
                                    <th>Landing Page</th>
                                    <th>Quiz Score</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($leads as $lead)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center text-white mr-3" style="width: 40px; height: 40px;">
                                                    <span class="font-weight-bold">{{ $lead->initials }}</span>
                                                </div>
                                                <div>
                                                    <strong>{{ $lead->name }}</strong>
                                                    @if($lead->availability)
                                                        <br><small class="text-success">
                                                            <i class="fas fa-calendar-check mr-1"></i>Availability provided
                                                        </small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <div class="mb-1">
                                                    <i class="fas fa-envelope mr-1 text-muted"></i>
                                                    <a href="mailto:{{ $lead->email }}">{{ $lead->email }}</a>
                                                </div>
                                                @if($lead->phone_number)
                                                    <div>
                                                        <i class="fas fa-phone mr-1 text-muted"></i>
                                                        <a href="tel:{{ $lead->phone_number }}">{{ $lead->phone_number }}</a>
                                                    </div>
                                                @endif
                                            </div>
                                        </td>
                                        <td>{{ $lead->company_name }}</td>
                                        <td>
                                            <a href="{{ route('admin.landing-pages.show', $lead->landingPage) }}">
                                                {{ $lead->landingPage->title }}
                                            </a>
                                        </td>
                                        <td>
                                            @if($lead->quiz_score !== null)
                                                <span class="badge badge-info">{{ $lead->quiz_score }}</span>
                                                @if($lead->pdf_sent)
                                                    <br><small class="text-success">
                                                        <i class="fas fa-file-pdf mr-1"></i>PDF sent
                                                    </small>
                                                @endif
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div>{{ $lead->created_at->format('M j, Y') }}</div>
                                            <small class="text-muted">{{ $lead->created_at->format('g:i A') }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ route('admin.leads.show', $lead) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.leads.edit', $lead) }}" class="btn btn-sm btn-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form method="POST" action="{{ route('admin.leads.destroy', $lead) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this lead?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Leads Found</h5>
                            @if(request()->hasAny(['landing_page_id', 'date_from', 'date_to', 'search']))
                                <p class="text-muted">Try adjusting your filters to see more results.</p>
                                <a href="{{ route('admin.leads.index') }}" class="btn btn-primary">
                                    <i class="fas fa-times mr-2"></i>Clear Filters
                                </a>
                            @else
                                <p class="text-muted">Leads will appear here when visitors complete landing page assessments.</p>
                                <a href="{{ route('admin.landing-pages.index') }}" class="btn btn-warning">
                                    <i class="fas fa-file-alt mr-2"></i>Manage Landing Pages
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
                <!-- /.card-body -->
                @if($leads->hasPages())
                    <div class="card-footer clearfix">
                        {{ $leads->appends(request()->query())->links() }}
                    </div>
                @endif
            </div>
            <!-- /.card -->


        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
