<!-- Score Configuration Form Content -->
<form id="score-config-form" method="POST">
    @csrf
    @method('PUT')

    <!-- Tabbed Interface -->
    <div class="nav-tabs-wrapper">
        <ul class="nav nav-tabs nav-tabs-custom" role="tablist">
            <li class="nav-item">
                <a class="nav-link active text-dark" id="modal-gauge-tab" data-toggle="tab" href="#modal-gauge" role="tab">
                    <i class="fas fa-gauge-high mr-2"></i>Gauge Display
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-dark" id="modal-colors-tab" data-toggle="tab" href="#modal-colors" role="tab">
                    <i class="fas fa-palette mr-2"></i>Color Ranges
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-dark" id="modal-results-tab" data-toggle="tab" href="#modal-results" role="tab">
                    <i class="fas fa-comment-alt mr-2"></i>Result Messages
                </a>
            </li>
        </ul>
    </div>

    <div class="tab-content mt-3">
        <!-- Gauge Display Tab -->
        <div class="tab-pane fade show active" id="modal-gauge" role="tabpanel">
            <h6 class="mb-3 text-dark">Gauge Display Settings</h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title text-dark">
                                <i class="fas fa-cog mr-2 text-primary"></i>Basic Settings
                            </h6>

                            <div class="form-group">
                                <label for="modal_gauge_config_min_value" class="text-dark">Minimum Value</label>
                                <input type="number" class="form-control" id="modal_gauge_config_min_value"
                                       name="gauge_config[min_value]" value="{{ $gaugeConfig['min_value'] ?? 0 }}" required>
                                <small class="form-text text-muted">The lowest possible score value</small>
                            </div>

                            <div class="form-group">
                                <label for="modal_gauge_config_max_value" class="text-dark">Maximum Value</label>
                                <input type="number" class="form-control" id="modal_gauge_config_max_value"
                                       name="gauge_config[max_value]" value="{{ $gaugeConfig['max_value'] ?? 100 }}" required>
                                <small class="form-text text-muted">The highest possible score value</small>
                            </div>

                            <div class="form-group">
                                <label for="modal_gauge_config_gauge_type" class="text-dark">Gauge Type</label>
                                <select class="form-control" id="modal_gauge_config_gauge_type" name="gauge_config[gauge_type]" required>
                                    <option value="semicircle" {{ ($gaugeConfig['gauge_type'] ?? 'semicircle') === 'semicircle' ? 'selected' : '' }}>
                                        Semi-circle
                                    </option>
                                    <option value="full_circle" {{ ($gaugeConfig['gauge_type'] ?? 'semicircle') === 'full_circle' ? 'selected' : '' }}>
                                        Full Circle
                                    </option>
                                    <option value="linear" {{ ($gaugeConfig['gauge_type'] ?? 'semicircle') === 'linear' ? 'selected' : '' }}>
                                        Linear Bar
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title text-dark">
                                <i class="fas fa-eye mr-2 text-success"></i>Display Options
                            </h6>

                            <div class="form-group">
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="modal_gauge_config_show_percentage"
                                           name="gauge_config[show_percentage]" value="1"
                                           {{ ($gaugeConfig['show_percentage'] ?? true) ? 'checked' : '' }}>
                                    <label class="custom-control-label text-dark" for="modal_gauge_config_show_percentage">
                                        Show Percentage
                                    </label>
                                </div>
                                <small class="form-text text-muted">Display score as percentage</small>
                            </div>

                            <div class="form-group">
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="modal_gauge_config_show_labels"
                                           name="gauge_config[show_labels]" value="1"
                                           {{ ($gaugeConfig['show_labels'] ?? true) ? 'checked' : '' }}>
                                    <label class="custom-control-label text-dark" for="modal_gauge_config_show_labels">
                                        Show Labels
                                    </label>
                                </div>
                                <small class="form-text text-muted">Display range labels on gauge</small>
                            </div>

                            <div class="form-group">
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="modal_gauge_config_animate"
                                           name="gauge_config[animate]" value="1"
                                           {{ ($gaugeConfig['animate'] ?? true) ? 'checked' : '' }}>
                                    <label class="custom-control-label text-dark" for="modal_gauge_config_animate">
                                        Animate Gauge
                                    </label>
                                </div>
                                <small class="form-text text-muted">Animate gauge when displaying score</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Color Ranges Tab -->
        <div class="tab-pane fade" id="modal-colors" role="tabpanel">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0 text-dark">Color Ranges Configuration</h6>
                <button type="button" class="btn btn-success btn-sm" id="modal-add-color-range">
                    <i class="fas fa-plus mr-1"></i>Add Range
                </button>
            </div>

            <div class="alert alert-info">
                <i class="fas fa-info-circle mr-2"></i>
                <strong>Color ranges</strong> determine how the gauge is colored based on score values.
                Ensure ranges cover the full score spectrum without gaps.
            </div>

            <div id="modal-color-ranges-container">
                @php
                    $defaultColorRanges = [
                        ['min' => 0, 'max' => 30, 'color' => '#dc3545', 'label' => 'Poor'],
                        ['min' => 31, 'max' => 60, 'color' => '#ffc107', 'label' => 'Fair'],
                        ['min' => 61, 'max' => 80, 'color' => '#17a2b8', 'label' => 'Good'],
                        ['min' => 81, 'max' => 100, 'color' => '#28a745', 'label' => 'Excellent']
                    ];
                    $colorRanges = $gaugeConfig['color_ranges'] ?? $defaultColorRanges;
                @endphp
                @foreach($colorRanges as $index => $range)
                    <div class="color-range-item border rounded p-3 mb-3" data-index="{{ $index }}">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0 text-dark">Range {{ $index + 1 }}</h6>
                            <button type="button" class="btn btn-danger btn-sm remove-color-range">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <label class="text-dark">Min Value</label>
                                <input type="number" class="form-control"
                                       name="gauge_config[color_ranges][{{ $index }}][min]"
                                       value="{{ $range['min'] ?? 0 }}" required>
                            </div>
                            <div class="col-md-3">
                                <label class="text-dark">Max Value</label>
                                <input type="number" class="form-control"
                                       name="gauge_config[color_ranges][{{ $index }}][max]"
                                       value="{{ $range['max'] ?? 100 }}" required>
                            </div>
                            <div class="col-md-3">
                                <label class="text-dark">Color</label>
                                <input type="color" class="form-control"
                                       name="gauge_config[color_ranges][{{ $index }}][color]"
                                       value="{{ $range['color'] ?? '#007bff' }}" required>
                            </div>
                            <div class="col-md-3">
                                <label class="text-dark">Label</label>
                                <input type="text" class="form-control"
                                       name="gauge_config[color_ranges][{{ $index }}][label]"
                                       value="{{ $range['label'] ?? 'Range ' . ($index + 1) }}" required
                                       placeholder="e.g., Excellent">
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Result Messages Tab -->
        <div class="tab-pane fade" id="modal-results" role="tabpanel">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0 text-dark">Result Messages Configuration</h6>
                <button type="button" class="btn btn-success btn-sm" id="modal-add-result-text">
                    <i class="fas fa-plus mr-1"></i>Add Result Range
                </button>
            </div>

            <div class="alert alert-info">
                <i class="fas fa-info-circle mr-2"></i>
                <strong>Result messages</strong> are shown to users based on their quiz score.
                Create personalized messages that provide value and encourage consultation booking.
            </div>

            <div id="modal-result-texts-container">
                @php
                    $defaultResultTexts = [
                        [
                            'min' => 0, 'max' => 30,
                            'title' => 'Limited Automation Readiness',
                            'description' => 'Your business shows potential for automation but may need foundational improvements first.'
                        ],
                        [
                            'min' => 31, 'max' => 60,
                            'title' => 'Moderate Automation Potential',
                            'description' => 'Your business has good automation opportunities with some strategic planning.'
                        ],
                        [
                            'min' => 61, 'max' => 80,
                            'title' => 'Strong Automation Candidate',
                            'description' => 'Your business is well-positioned for automation implementation with significant ROI potential.'
                        ],
                        [
                            'min' => 81, 'max' => 100,
                            'title' => 'Excellent Automation Readiness',
                            'description' => 'Your business is perfectly positioned for automation with maximum efficiency gains and ROI.'
                        ]
                    ];
                    $resultRanges = $resultTexts['ranges'] ?? $defaultResultTexts;
                @endphp
                @foreach($resultRanges as $index => $range)
                    <div class="result-text-item border rounded p-3 mb-3" data-index="{{ $index }}">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0 text-dark">Result Range {{ $index + 1 }}</h6>
                            <button type="button" class="btn btn-danger btn-sm remove-result-text">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="text-dark">Min Score</label>
                                <input type="number" class="form-control"
                                       name="result_texts[ranges][{{ $index }}][min]"
                                       value="{{ $range['min'] ?? 0 }}" required>
                            </div>
                            <div class="col-md-6">
                                <label class="text-dark">Max Score</label>
                                <input type="number" class="form-control"
                                       name="result_texts[ranges][{{ $index }}][max]"
                                       value="{{ $range['max'] ?? 100 }}" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="text-dark">Result Title</label>
                            <input type="text" class="form-control"
                                   name="result_texts[ranges][{{ $index }}][title]"
                                   value="{{ $range['title'] ?? '' }}" required
                                   placeholder="e.g., Excellent Automation Readiness">
                        </div>
                        <div class="form-group">
                            <label class="text-dark">Description</label>
                            <textarea class="form-control" rows="3"
                                      name="result_texts[ranges][{{ $index }}][description]"
                                      required placeholder="Detailed explanation of the result...">{{ $range['description'] ?? '' }}</textarea>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</form>

<style>
.nav-tabs-custom {
    border-bottom: 2px solid #dee2e6;
}

.nav-tabs-custom .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 0.75rem 1rem;
}

.nav-tabs-custom .nav-link:hover {
    border-color: transparent;
    color: #495057;
    background-color: #f8f9fa;
}

.nav-tabs-custom .nav-link.active {
    color: #007bff;
    background-color: transparent;
    border-bottom-color: #007bff;
}

.color-range-item,
.result-text-item {
    background-color: #f8f9fa;
    transition: all 0.2s ease;
}

.color-range-item:hover,
.result-text-item:hover {
    background-color: #e9ecef;
}

.modal-xl .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}
</style>
