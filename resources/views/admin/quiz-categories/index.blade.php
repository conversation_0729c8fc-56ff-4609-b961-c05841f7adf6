@extends('components.layouts.admin')

@section('title', 'Quiz Categories - ' . $landingPage->title)

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Quiz Categories</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.landing-pages.index') }}">Landing Pages</a></li>
                        <li class="breadcrumb-item active">Quiz Categories</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Action Buttons -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-outline-primary mr-2">
                                <i class="fas fa-question-circle mr-2"></i>Questions
                            </a>
                            <a href="{{ route('admin.landing-pages.show', $landingPage) }}" class="btn btn-outline-secondary mr-2">
                                <i class="fas fa-file-alt mr-2"></i>Landing Page
                            </a>
                            <a href="{{ route('admin.landing-pages.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left mr-2"></i>Back
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            @if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ session('error') }}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
@endif

<div class="row">
    <div class="col-lg-8">
        <!-- Categories List -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Categories ({{ $categories->count() }})</h5>
                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addCategoryModal">
                    <i class="fas fa-plus mr-2"></i>Add Category
                </button>
            </div>
            <div class="card-body">
                @if($categories->count() > 0)
                    <div id="categories-list">
                        @foreach($categories as $category)
                            <div class="category-item border rounded p-3 mb-3" data-category-id="{{ $category->id }}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            <span class="badge mr-2" style="background-color: {{ $category->color }}; color: white;">
                                                {{ $category->sort_order }}
                                            </span>
                                            <h6 class="mb-0">{{ $category->name }}</h6>
                                            @if(!$category->is_active)
                                                <span class="badge bg-secondary ml-2">Inactive</span>
                                            @endif
                                        </div>

                                        @if($category->description)
                                            <p class="text-muted small mb-2">{{ $category->description }}</p>
                                        @endif

                                        <div class="category-stats">
                                            <small class="text-muted">
                                                <i class="fas fa-question-circle mr-1"></i>
                                                {{ $category->getQuestionCount() }} questions
                                                <span class="mx-2">•</span>
                                                <i class="fas fa-star mr-1"></i>
                                                {{ $category->getTotalPoints() }} total points
                                            </small>
                                        </div>
                                    </div>
                                    <div class="btn-group ml-3">
                                        <button type="button" class="btn btn-sm btn-outline-secondary"
                                                data-toggle="modal"
                                                data-target="#editCategoryModal{{ $category->id }}">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                data-toggle="modal"
                                                data-target="#deleteCategoryModal{{ $category->id }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary drag-handle">
                                            <i class="fas fa-grip-vertical"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Edit Category Modal -->
                            <div class="modal fade" id="editCategoryModal{{ $category->id }}" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Edit Category</h5>
                                            <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
                                        </div>
                                        <form method="POST" action="{{ route('admin.quiz-categories.update', [$landingPage, $category]) }}">
                                            @csrf
                                            @method('PUT')
                                            <div class="modal-body">
                                                <div class="mb-3">
                                                    <label class="form-label">Category Name</label>
                                                    <input type="text" class="form-control" name="name" value="{{ $category->name }}" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Description</label>
                                                    <textarea class="form-control" name="description" rows="3">{{ $category->description }}</textarea>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label class="form-label">Color</label>
                                                            <input type="color" class="form-control form-control-color" name="color" value="{{ $category->color }}">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-check form-switch mt-4">
                                                            <input class="form-check-input" type="checkbox" name="is_active" value="1" {{ $category->is_active ? 'checked' : '' }}>
                                                            <label class="form-check-label">Active</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                <button type="submit" class="btn btn-primary">Update Category</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Delete Category Modal -->
                            <div class="modal fade" id="deleteCategoryModal{{ $category->id }}" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Delete Category</h5>
                                            <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Are you sure you want to delete this category?</p>
                                            <p class="text-muted small">{{ $category->name }}</p>
                                            @if($category->getQuestionCount() > 0)
                                                <div class="alert alert-warning">
                                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                                    This category contains {{ $category->getQuestionCount() }} question(s).
                                                    Please move or delete the questions first.
                                                </div>
                                            @endif
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                            @if($category->getQuestionCount() == 0)
                                                <form method="POST" action="{{ route('admin.quiz-categories.destroy', [$landingPage, $category]) }}" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger">Delete</button>
                                                </form>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No categories yet</h6>
                        <p class="text-muted">Create categories to organize your quiz questions.</p>
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addCategoryModal">
                            <i class="fas fa-plus mr-2"></i>Add First Category
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Quiz Overview -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Quiz Overview</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-primary mb-1">{{ $categories->count() }}</h4>
                            <small class="text-muted">Categories</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-info mb-1">{{ $quiz->questions->count() }}</h4>
                            <small class="text-muted">Questions</small>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="border rounded p-3">
                            <h4 class="text-success mb-1">{{ $quiz->getTotalPossibleScore() }}</h4>
                            <small class="text-muted">Max Score</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary" data-toggle="modal" data-target="#addCategoryModal">
                        <i class="fas fa-plus mr-2"></i>Add Category
                    </button>
                    <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-question-circle mr-2"></i>Manage Questions
                    </a>
                    <a href="{{ route('landing-pages.quiz', $landingPage) }}" target="_blank" class="btn btn-outline-success">
                        <i class="fas fa-external-link-alt mr-2"></i>Preview Quiz
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Category</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <form method="POST" action="{{ route('admin.quiz-categories.store', $landingPage) }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Category Name *</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="color" class="form-label">Color</label>
                                <input type="color" class="form-control form-control-color" id="color" name="color" value="#007bff">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mt-4">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                <label class="form-check-label" for="is_active">Active</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize sortable for categories
    if (typeof Sortable !== 'undefined') {
        const categoriesList = document.getElementById('categories-list');
        if (categoriesList) {
            new Sortable(categoriesList, {
                handle: '.drag-handle',
                animation: 150,
                onEnd: function(evt) {
                    const categoryIds = Array.from(categoriesList.children).map(item =>
                        item.getAttribute('data-category-id')
                    );

                    fetch('{{ route("admin.quiz-categories.reorder", $landingPage) }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            categories: categoryIds
                        })
                    });
                }
            });
        }
    }
});
</script>
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
