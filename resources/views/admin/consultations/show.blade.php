@extends('components.layouts.admin')

@section('title', __('View Consultation'))

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-user-tie text-primary mr-2"></i>
                        {{ __('Consultation Details') }}
                    </h1>
                    <p class="text-muted mb-0">{{ __('Submitted by') }} {{ $consultation->name }} • {{ $consultation->created_at->diffForHumans() }}</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.consultations.index') }}">Consultations</a></li>
                        <li class="breadcrumb-item active">{{ $consultation->name }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Quick Actions Row -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    @if ($consultation->status)
                                        <span class="badge badge-lg mr-3" style="background-color: {{ $consultation->status->color }}; font-size: 0.9rem; padding: 0.5rem 1rem;">
                                            <i class="fas fa-circle mr-1"></i>
                                            {{ $consultation->status->name }}
                                        </span>
                                    @else
                                        <span class="badge badge-secondary badge-lg mr-3" style="font-size: 0.9rem; padding: 0.5rem 1rem;">
                                            <i class="fas fa-question-circle mr-1"></i>
                                            {{ __('No Status') }}
                                        </span>
                                    @endif

                                    <div class="btn-group">
                                        <button type="button" class="btn btn-outline-primary btn-sm dropdown-toggle" data-toggle="dropdown">
                                            <i class="fas fa-exchange-alt mr-1"></i>
                                            {{ __('Change Status') }}
                                        </button>
                                        <div class="dropdown-menu">
                                            @foreach($statuses as $status)
                                                @if(!$consultation->status || $consultation->status->id != $status->id)
                                                    <form action="{{ route('admin.consultations.update-status', $consultation) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('PATCH')
                                                        <input type="hidden" name="status_id" value="{{ $status->id }}">
                                                        <button type="submit" class="dropdown-item">
                                                            <span class="badge mr-2" style="background-color: {{ $status->color }};">
                                                                &nbsp;
                                                            </span>
                                                            {{ $status->name }}
                                                            @if($status->description)
                                                                <small class="text-muted d-block">{{ $status->description }}</small>
                                                            @endif
                                                        </button>
                                                    </form>
                                                @endif
                                            @endforeach
                                        </div>
                                    </div>
                                </div>

                                <div class="btn-group">
                                    <a href="{{ route('admin.consultations.edit', $consultation) }}" class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit mr-1"></i>{{ __('Edit') }}
                                    </a>
                                    <a href="{{ route('admin.consultations.index') }}" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-arrow-left mr-1"></i>{{ __('Back') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information Card -->
            <div class="row">
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-address-card mr-2"></i>
                                {{ __('Contact Information') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-user text-primary mr-2"></i>
                                    <strong>{{ __('Full Name') }}</strong>
                                </div>
                                <p class="mb-0 ml-4">{{ $consultation->name }}</p>
                            </div>

                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-envelope text-primary mr-2"></i>
                                    <strong>{{ __('Email Address') }}</strong>
                                </div>
                                <p class="mb-0 ml-4">
                                    <a href="mailto:{{ $consultation->email }}" class="text-decoration-none">
                                        {{ $consultation->email }}
                                    </a>
                                </p>
                            </div>

                            <div class="info-item">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-globe text-primary mr-2"></i>
                                    <strong>{{ __('Website') }}</strong>
                                </div>
                                <p class="mb-0 ml-4">
                                    @if ($consultation->website_url)
                                        <a href="{{ $consultation->website_url }}" target="_blank" class="text-decoration-none">
                                            {{ $consultation->website_url }}
                                            <i class="fas fa-external-link-alt ml-1 text-muted"></i>
                                        </a>
                                    @else
                                        <span class="text-muted">{{ __('Not provided') }}</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Timeline & Status Card -->
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock mr-2"></i>
                                {{ __('Timeline & Status') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-calendar-plus text-info mr-2"></i>
                                    <strong>{{ __('Submitted') }}</strong>
                                </div>
                                <p class="mb-1 ml-4">{{ $consultation->created_at->format('F j, Y') }}</p>
                                <p class="mb-0 ml-4 text-muted small">{{ $consultation->created_at->format('g:i A') }} • {{ $consultation->created_at->diffForHumans() }}</p>
                            </div>

                            @if($consultation->updated_at && $consultation->updated_at != $consultation->created_at)
                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-edit text-info mr-2"></i>
                                    <strong>{{ __('Last Updated') }}</strong>
                                </div>
                                <p class="mb-1 ml-4">{{ $consultation->updated_at->format('F j, Y') }}</p>
                                <p class="mb-0 ml-4 text-muted small">{{ $consultation->updated_at->format('g:i A') }} • {{ $consultation->updated_at->diffForHumans() }}</p>
                            </div>
                            @endif

                            <div class="info-item">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-flag text-info mr-2"></i>
                                    <strong>{{ __('Current Status') }}</strong>
                                </div>
                                <div class="ml-4">
                                    @if ($consultation->status)
                                        <span class="badge badge-lg" style="background-color: {{ $consultation->status->color }}; font-size: 0.85rem; padding: 0.4rem 0.8rem;">
                                            {{ $consultation->status->name }}
                                        </span>
                                        @if($consultation->status->description)
                                            <p class="mb-0 mt-2 text-muted small">{{ $consultation->status->description }}</p>
                                        @endif
                                    @else
                                        <span class="badge badge-secondary badge-lg" style="font-size: 0.85rem; padding: 0.4rem 0.8rem;">
                                            {{ __('No Status') }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats Card -->
                <div class="col-lg-4 col-md-12">
                    <div class="card h-100">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line mr-2"></i>
                                {{ __('Quick Overview') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="info-box-content">
                                        <span class="info-box-text">{{ __('Days Since') }}</span>
                                        <span class="info-box-number text-warning">
                                            {{ $consultation->created_at->diffInDays(now()) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="info-box-content">
                                        <span class="info-box-text">{{ __('Priority') }}</span>
                                        <span class="info-box-number">
                                            @if($consultation->created_at->diffInDays(now()) > 7)
                                                <span class="text-danger">{{ __('High') }}</span>
                                            @elseif($consultation->created_at->diffInDays(now()) > 3)
                                                <span class="text-warning">{{ __('Medium') }}</span>
                                            @else
                                                <span class="text-success">{{ __('Normal') }}</span>
                                            @endif
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="text-center">
                                <small class="text-muted">
                                    {{ __('Consultation ID') }}: #{{ str_pad($consultation->id, 4, '0', STR_PAD_LEFT) }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Information Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-building mr-2"></i>
                                {{ __('Business Information') }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="info-section mb-4">
                                        <div class="d-flex align-items-center mb-3">
                                            <i class="fas fa-info-circle text-success mr-2"></i>
                                            <h6 class="mb-0 font-weight-bold">{{ __('Business Description') }}</h6>
                                        </div>
                                        <div class="bg-light p-3 rounded">
                                            <p class="mb-0">{{ $consultation->business_description }}</p>
                                        </div>
                                    </div>

                                    <div class="info-section">
                                        <div class="d-flex align-items-center mb-3">
                                            <i class="fas fa-exclamation-triangle text-warning mr-2"></i>
                                            <h6 class="mb-0 font-weight-bold">{{ __('Pain Points') }}</h6>
                                        </div>
                                        <div class="bg-light p-3 rounded">
                                            <p class="mb-0">{{ $consultation->pain_point }}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-6">
                                    <div class="info-section mb-4">
                                        <div class="d-flex align-items-center mb-3">
                                            <i class="fas fa-cogs text-info mr-2"></i>
                                            <h6 class="mb-0 font-weight-bold">{{ __('Current Automation') }}</h6>
                                        </div>
                                        <div class="bg-light p-3 rounded">
                                            <p class="mb-0">{{ $consultation->current_automation }}</p>
                                        </div>
                                    </div>

                                    @if ($consultation->additional_notes)
                                    <div class="info-section">
                                        <div class="d-flex align-items-center mb-3">
                                            <i class="fas fa-sticky-note text-secondary mr-2"></i>
                                            <h6 class="mb-0 font-weight-bold">{{ __('Additional Notes') }}</h6>
                                        </div>
                                        <div class="bg-light p-3 rounded">
                                            <p class="mb-0">{{ $consultation->additional_notes }}</p>
                                        </div>
                                    </div>
                                    @else
                                    <div class="info-section">
                                        <div class="d-flex align-items-center mb-3">
                                            <i class="fas fa-sticky-note text-muted mr-2"></i>
                                            <h6 class="mb-0 font-weight-bold text-muted">{{ __('Additional Notes') }}</h6>
                                        </div>
                                        <div class="bg-light p-3 rounded">
                                            <p class="mb-0 text-muted font-italic">{{ __('No additional notes provided') }}</p>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-2">{{ __('Consultation Actions') }}</h6>
                                    <p class="text-muted mb-0 small">{{ __('Manage this consultation request') }}</p>
                                </div>
                                <div class="btn-group">
                                    <a href="{{ route('admin.consultations.edit', $consultation) }}" class="btn btn-warning">
                                        <i class="fas fa-edit mr-2"></i>{{ __('Edit Details') }}
                                    </a>
                                    <a href="mailto:{{ $consultation->email }}?subject=Re: Your Consultation Request" class="btn btn-info">
                                        <i class="fas fa-envelope mr-2"></i>{{ __('Send Email') }}
                                    </a>
                                    <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#deleteModal">
                                        <i class="fas fa-trash mr-2"></i>{{ __('Delete') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Delete Confirmation Modal -->
            <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title" id="deleteModalLabel">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                {{ __('Confirm Deletion') }}
                            </h5>
                            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <p>{{ __('Are you sure you want to delete this consultation request?') }}</p>
                            <div class="alert alert-warning">
                                <strong>{{ __('Warning:') }}</strong> {{ __('This action cannot be undone. All consultation data will be permanently removed.') }}
                            </div>
                            <div class="bg-light p-3 rounded">
                                <strong>{{ __('Consultation Details:') }}</strong><br>
                                <strong>{{ __('Name:') }}</strong> {{ $consultation->name }}<br>
                                <strong>{{ __('Email:') }}</strong> {{ $consultation->email }}<br>
                                <strong>{{ __('Submitted:') }}</strong> {{ $consultation->created_at->format('F j, Y \a\t g:i A') }}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                <i class="fas fa-times mr-2"></i>{{ __('Cancel') }}
                            </button>
                            <form action="{{ route('admin.consultations.destroy', $consultation) }}" method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash mr-2"></i>{{ __('Delete Consultation') }}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
