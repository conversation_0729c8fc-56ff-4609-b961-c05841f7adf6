@extends('components.layouts.admin')

@section('title', __('Edit Consultation'))

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-edit text-warning mr-2"></i>
                        {{ __('Edit Consultation') }}
                    </h1>
                    <p class="text-muted mb-0">{{ __('Editing consultation from') }} {{ $consultation->name }} • {{ __('ID') }}: #{{ str_pad($consultation->id, 4, '0', STR_PAD_LEFT) }}</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.consultations.index') }}">Consultations</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.consultations.show', $consultation) }}">{{ $consultation->name }}</a></li>
                        <li class="breadcrumb-item active">Edit</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Quick Info Bar -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    @if ($consultation->status)
                                        <span class="badge badge-lg mr-3" style="background-color: {{ $consultation->status->color }}; font-size: 0.9rem; padding: 0.5rem 1rem;">
                                            <i class="fas fa-circle mr-1"></i>
                                            {{ $consultation->status->name }}
                                        </span>
                                    @else
                                        <span class="badge badge-secondary badge-lg mr-3" style="font-size: 0.9rem; padding: 0.5rem 1rem;">
                                            <i class="fas fa-question-circle mr-1"></i>
                                            {{ __('No Status') }}
                                        </span>
                                    @endif
                                    <small class="text-muted">
                                        {{ __('Submitted') }} {{ $consultation->created_at->diffForHumans() }} •
                                        {{ __('Last updated') }} {{ $consultation->updated_at->diffForHumans() }}
                                    </small>
                                </div>

                                <div class="btn-group">
                                    <a href="{{ route('admin.consultations.show', $consultation) }}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye mr-1"></i>{{ __('View') }}
                                    </a>
                                    <a href="{{ route('admin.consultations.index') }}" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-arrow-left mr-1"></i>{{ __('Back') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <form action="{{ route('admin.consultations.update', $consultation) }}" method="POST">
                @csrf
                @method('PUT')

                <!-- Contact Information Card -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-address-card mr-2"></i>
                                    {{ __('Contact Information') }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="form-group mb-3">
                                    <label for="name" class="form-label">
                                        <i class="fas fa-user text-primary mr-1"></i>
                                        {{ __('Full Name') }} <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" name="name" id="name" value="{{ old('name', $consultation->name) }}"
                                           class="form-control @error('name') is-invalid @enderror" required
                                           placeholder="{{ __('Enter full name') }}">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope text-primary mr-1"></i>
                                        {{ __('Email Address') }} <span class="text-danger">*</span>
                                    </label>
                                    <input type="email" name="email" id="email" value="{{ old('email', $consultation->email) }}"
                                           class="form-control @error('email') is-invalid @enderror" required
                                           placeholder="{{ __('Enter email address') }}">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="website_url" class="form-label">
                                        <i class="fas fa-globe text-primary mr-1"></i>
                                        {{ __('Website URL') }}
                                    </label>
                                    <input type="url" name="website_url" id="website_url" value="{{ old('website_url', $consultation->website_url) }}"
                                           class="form-control @error('website_url') is-invalid @enderror"
                                           placeholder="{{ __('https://example.com') }}">
                                    @error('website_url')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">{{ __('Optional: Enter the client\'s website URL') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Status Information Card -->
                    <div class="col-lg-6">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-flag mr-2"></i>
                                    {{ __('Status & Timeline') }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="form-group mb-3">
                                    <label for="status_id" class="form-label">
                                        <i class="fas fa-tasks text-info mr-1"></i>
                                        {{ __('Consultation Status') }} <span class="text-danger">*</span>
                                    </label>
                                    <select name="status_id" id="status_id" class="form-control @error('status_id') is-invalid @enderror" required>
                                        @foreach($statuses as $status)
                                            <option value="{{ $status->id }}"
                                                {{ old('status_id', $consultation->status_id) == $status->id ? 'selected' : '' }}
                                                data-color="{{ $status->color }}"
                                                data-description="{{ $status->description }}">
                                                {{ $status->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('status_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group mb-3">
                                    <label class="form-label">{{ __('Status Preview') }}</label>
                                    <div id="status-preview" class="alert border" style="background-color: {{ old('status_id', $consultation->status) ? $consultation->status->color : '#6c757d' }}; color: white;">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-circle mr-2"></i>
                                            <strong id="status-name">
                                                @if($consultation->status)
                                                    {{ $consultation->status->name }}
                                                @else
                                                    {{ __('No Status') }}
                                                @endif
                                            </strong>
                                        </div>
                                        <div id="status-description" class="small mt-1">
                                            @if($consultation->status && $consultation->status->description)
                                                {{ $consultation->status->description }}
                                            @else
                                                {{ __('No description available') }}
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">{{ __('Timeline Information') }}</label>
                                    <div class="bg-light p-3 rounded">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-calendar-plus text-info mr-2"></i>
                                            <strong>{{ __('Submitted:') }}</strong>
                                        </div>
                                        <p class="mb-2 ml-4">{{ $consultation->created_at->format('F j, Y \a\t g:i A') }}</p>
                                        <p class="mb-0 ml-4 text-muted small">{{ $consultation->created_at->diffForHumans() }}</p>

                                        @if($consultation->updated_at && $consultation->updated_at != $consultation->created_at)
                                        <hr class="my-2">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-edit text-info mr-2"></i>
                                            <strong>{{ __('Last Updated:') }}</strong>
                                        </div>
                                        <p class="mb-2 ml-4">{{ $consultation->updated_at->format('F j, Y \a\t g:i A') }}</p>
                                        <p class="mb-0 ml-4 text-muted small">{{ $consultation->updated_at->diffForHumans() }}</p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Business Information Section -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-building mr-2"></i>
                                    {{ __('Business Information') }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group mb-4">
                                            <label for="business_description" class="form-label">
                                                <i class="fas fa-info-circle text-success mr-1"></i>
                                                {{ __('Business Description') }} <span class="text-danger">*</span>
                                            </label>
                                            <textarea name="business_description" id="business_description" rows="5"
                                                      class="form-control @error('business_description') is-invalid @enderror" required
                                                      placeholder="{{ __('Describe the client\'s business, industry, and main activities...') }}">{{ old('business_description', $consultation->business_description) }}</textarea>
                                            @error('business_description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">{{ __('Provide a detailed description of the client\'s business') }}</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="pain_point" class="form-label">
                                                <i class="fas fa-exclamation-triangle text-warning mr-1"></i>
                                                {{ __('Pain Points') }} <span class="text-danger">*</span>
                                            </label>
                                            <textarea name="pain_point" id="pain_point" rows="5"
                                                      class="form-control @error('pain_point') is-invalid @enderror" required
                                                      placeholder="{{ __('What challenges and problems is the client facing?') }}">{{ old('pain_point', $consultation->pain_point) }}</textarea>
                                            @error('pain_point')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">{{ __('Describe the specific problems and challenges the client wants to solve') }}</small>
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="form-group mb-4">
                                            <label for="current_automation" class="form-label">
                                                <i class="fas fa-cogs text-info mr-1"></i>
                                                {{ __('Current Automation') }} <span class="text-danger">*</span>
                                            </label>
                                            <textarea name="current_automation" id="current_automation" rows="5"
                                                      class="form-control @error('current_automation') is-invalid @enderror" required
                                                      placeholder="{{ __('What automation tools or processes does the client currently use?') }}">{{ old('current_automation', $consultation->current_automation) }}</textarea>
                                            @error('current_automation')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">{{ __('Describe existing automation, tools, and processes') }}</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="additional_notes" class="form-label">
                                                <i class="fas fa-sticky-note text-secondary mr-1"></i>
                                                {{ __('Additional Notes') }}
                                            </label>
                                            <textarea name="additional_notes" id="additional_notes" rows="5"
                                                      class="form-control @error('additional_notes') is-invalid @enderror"
                                                      placeholder="{{ __('Any additional information, special requirements, or notes...') }}">{{ old('additional_notes', $consultation->additional_notes) }}</textarea>
                                            @error('additional_notes')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">{{ __('Optional: Any additional information that might be helpful') }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-2">{{ __('Save Changes') }}</h6>
                                        <p class="text-muted mb-0 small">{{ __('Review your changes and save the consultation') }}</p>
                                    </div>
                                    <div class="btn-group">
                                        <button type="submit" class="btn btn-success btn-lg">
                                            <i class="fas fa-save mr-2"></i>{{ __('Update Consultation') }}
                                        </button>
                                        <a href="{{ route('admin.consultations.show', $consultation) }}" class="btn btn-info">
                                            <i class="fas fa-eye mr-2"></i>{{ __('View Details') }}
                                        </a>
                                        <a href="{{ route('admin.consultations.index') }}" class="btn btn-secondary">
                                            <i class="fas fa-times mr-2"></i>{{ __('Cancel') }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const statusSelect = document.getElementById('status_id');
            const statusPreview = document.getElementById('status-preview');
            const statusName = document.getElementById('status-name');
            const statusDescription = document.getElementById('status-description');

            // Store status descriptions
            const statusData = {
                @foreach($statuses as $status)
                    {{ $status->id }}: {
                        name: "{{ $status->name }}",
                        color: "{{ $status->color }}",
                        description: "{{ $status->description ?: __('No description available') }}"
                    },
                @endforeach
            };

            function updateStatusPreview() {
                const selectedStatusId = statusSelect.value;
                const statusInfo = statusData[selectedStatusId];

                if (statusInfo) {
                    statusPreview.style.backgroundColor = statusInfo.color;
                    statusName.textContent = statusInfo.name;
                    statusDescription.textContent = statusInfo.description;
                }
            }

            // Update preview when status changes
            statusSelect.addEventListener('change', updateStatusPreview);

            // Form validation enhancement
            const form = statusSelect.closest('form');
            const requiredFields = form.querySelectorAll('[required]');

            form.addEventListener('submit', function(e) {
                let isValid = true;

                requiredFields.forEach(function(field) {
                    if (!field.value.trim()) {
                        field.classList.add('is-invalid');
                        isValid = false;
                    } else {
                        field.classList.remove('is-invalid');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    // Scroll to first invalid field
                    const firstInvalid = form.querySelector('.is-invalid');
                    if (firstInvalid) {
                        firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        firstInvalid.focus();
                    }
                }
            });

            // Real-time validation for required fields
            requiredFields.forEach(function(field) {
                field.addEventListener('blur', function() {
                    if (!this.value.trim()) {
                        this.classList.add('is-invalid');
                    } else {
                        this.classList.remove('is-invalid');
                    }
                });

                field.addEventListener('input', function() {
                    if (this.value.trim()) {
                        this.classList.remove('is-invalid');
                    }
                });
            });
        });
    </script>
@endsection
