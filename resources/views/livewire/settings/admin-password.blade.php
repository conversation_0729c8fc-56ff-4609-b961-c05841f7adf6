<div>
    <div class="settings-heading">
        <h1>Password Settings</h1>
        <p>Ensure your account is using a long, random password to stay secure</p>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-body">
                    <form wire:submit="updatePassword">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Current Password</label>
                            <input wire:model="current_password" type="password" class="form-control" id="current_password" required>
                            @error('current_password') <div class="text-danger mt-1">{{ $message }}</div> @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">New Password</label>
                            <input wire:model="password" type="password" class="form-control" id="password" required>
                            @error('password') <div class="text-danger mt-1">{{ $message }}</div> @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="password_confirmation" class="form-label">Confirm Password</label>
                            <input wire:model="password_confirmation" type="password" class="form-control" id="password_confirmation" required>
                            @error('password_confirmation') <div class="text-danger mt-1">{{ $message }}</div> @enderror
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
