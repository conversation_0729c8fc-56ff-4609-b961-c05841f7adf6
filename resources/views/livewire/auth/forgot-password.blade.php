<?php

use Illuminate\Support\Facades\Password;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

new #[Layout('components.layouts.auth')] class extends Component {
    public string $email = '';

    /**
     * Send a password reset link to the provided email address.
     */
    public function sendPasswordResetLink(): void
    {
        $this->validate([
            'email' => ['required', 'string', 'email'],
        ]);

        Password::sendResetLink($this->only('email'));

        session()->flash('status', __('A reset link will be sent if the account exists.'));
    }
}; ?>

<div class="flex flex-col gap-4">
    <div class="text-center mb-4">
        <h2 class="fw-bold mb-2">{{ __('Forgot password') }}</h2>
        <p class="text-muted">{{ __('Enter your email to receive a password reset link') }}</p>
    </div>

    <!-- Session Status -->
    @if (session('status'))
        <div class="alert alert-success text-center">
            {{ session('status') }}
        </div>
    @endif

    <form wire:submit="sendPasswordResetLink" class="mb-3">
        <!-- Email Address -->
        <div class="mb-4">
            <label for="email" class="form-label">{{ __('Email Address') }}</label>
            <input
                wire:model="email"
                id="email"
                type="email"
                class="form-control @error('email') is-invalid @enderror"
                required
                autofocus
                placeholder="<EMAIL>"
            />
            @error('email')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="d-grid mb-4">
            <button type="submit" class="btn btn-primary">{{ __('Email password reset link') }}</button>
        </div>
    </form>

    <div class="text-center">
        <p class="text-muted">
            {{ __('Or, return to') }}
            <a href="{{ route('login') }}" class="text-decoration-none" wire:navigate>{{ __('log in') }}</a>
        </p>
    </div>
</div>
