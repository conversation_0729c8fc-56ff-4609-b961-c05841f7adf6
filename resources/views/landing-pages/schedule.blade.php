@extends('layouts.app')

@section('title', 'Schedule Your Consultation - ' . $landingPage->title)

@section('content')
<div class="schedule-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="schedule-header text-center mb-5">
                    <div class="success-icon mb-4">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h1>Assessment Complete!</h1>
                    <p class="lead">Thank you, {{ $lead->name }}! Your personalized report has been generated.</p>
                    @if($scoreData)
                        <div class="score-display">
                            <div class="score-gauge-container">
                                <div class="score-gauge"
                                     data-score="{{ $scoreData['score'] }}"
                                     data-max="{{ $scoreData['gauge_config']['max_value'] }}"
                                     data-color="{{ $scoreData['gauge_color'] }}"
                                     data-animate="{{ $scoreData['gauge_config']['animate'] ? 'true' : 'false' }}">
                                    <div class="gauge-background"></div>
                                    <div class="gauge-fill" style="--gauge-color: {{ $scoreData['gauge_color'] }}"></div>
                                    <div class="gauge-center">
                                        <span class="score-number">{{ $scoreData['score'] }}</span>
                                        @if($scoreData['gauge_config']['show_percentage'])
                                            <span class="score-unit">%</span>
                                        @endif
                                        <span class="score-label">{{ $scoreData['gauge_label'] }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($scoreData['result_text'])
                            <div class="assessment-result mt-4">
                                <div class="result-card">
                                    <h3 class="result-title">{{ $scoreData['result_text']['title'] }}</h3>
                                    <p class="result-description">{{ $scoreData['result_text']['description'] }}</p>

                                </div>
                            </div>
                        @endif
                    @endif
                </div>

                <div class="card">
                    <div class="card-body">
                        <h3 class="card-title text-center mb-4">
                            <i class="fas fa-calendar-alt me-2"></i>
                            Schedule Your Free Consultation
                        </h3>

                        <p class="text-center text-muted mb-4">
                            Based on your assessment results, we'd like to discuss how we can help optimize your business processes.
                            Please let us know your availability for a 30-minute consultation call.
                        </p>

                        <form method="POST" action="{{ route('landing-pages.schedule.submit', $landingPage) }}">
                            @csrf

                            <div class="mb-4">
                                <label for="availability" class="form-label">
                                    <strong>Your Availability</strong>
                                    <small class="text-muted d-block">Please provide your preferred days and times for a consultation call</small>
                                </label>
                                <textarea class="form-control"
                                          id="availability"
                                          name="availability"
                                          rows="6"
                                          required
                                          placeholder="Example:
• Monday-Friday: 9 AM - 5 PM EST
• Best times: Tuesday/Thursday mornings
• Prefer calls between 10 AM - 2 PM
• Available next week except Wednesday
• Time zone: Eastern Standard Time

Please include your time zone and any specific preferences.">{{ old('availability') }}</textarea>
                                @error('availability')
                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="consultation-benefits mb-4">
                                <h5>What to Expect in Your Consultation:</h5>
                                <ul class="benefits-list">
                                    <li><i class="fas fa-check text-success me-2"></i>Personalized analysis of your assessment results</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Custom recommendations for your business</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Discussion of automation opportunities</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Next steps and implementation roadmap</li>
                                    <li><i class="fas fa-check text-success me-2"></i>No obligation - completely free consultation</li>
                                </ul>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-calendar-check me-2"></i>
                                    Schedule My Free Consultation
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="contact-info text-center mt-4">
                    <p class="text-muted">
                        <small>
                            <i class="fas fa-clock me-1"></i>
                            We'll contact you within 24 hours to confirm your consultation time.
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.schedule-container {
    padding-top: 120px; /* Account for fixed navbar */
    padding-bottom: 3rem;
    min-height: 80vh;
}

.success-icon {
    font-size: 4rem;
    color: var(--success);
}

.schedule-header h1 {
    color: var(--dark);
    font-weight: 700;
    margin-bottom: 1rem;
}

.score-display {
    margin: 2rem 0;
}

.score-gauge-container {
    display: flex;
    justify-content: center;
    margin: 2rem 0;
}

.score-gauge {
    position: relative;
    width: 200px;
    height: 100px;
    margin-bottom: 1rem;
}

.gauge-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 8px solid var(--gray-light);
    border-bottom: none;
    border-radius: 100px 100px 0 0;
}

.gauge-fill {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 8px solid var(--gauge-color, var(--primary));
    border-bottom: none;
    border-radius: 100px 100px 0 0;
    clip-path: polygon(0 0, 0% 0, 0% 100%, 0 100%);
    transition: clip-path 2s ease-in-out;
}

.gauge-fill.animated {
    animation: fillGauge 2s ease-in-out forwards;
}

.gauge-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    margin-top: 10px;
}

.score-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--dark);
    line-height: 1;
}

.score-unit {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--gray);
    margin-left: 2px;
}

.score-label {
    display: block;
    font-size: 0.9rem;
    color: var(--gray);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 0.25rem;
}

@keyframes fillGauge {
    from {
        clip-path: polygon(0 0, 0% 0, 0% 100%, 0 100%);
    }
    to {
        clip-path: polygon(0 0, var(--fill-percentage, 50%) 0, var(--fill-percentage, 50%) 100%, 0 100%);
    }
}

/* Assessment Result Styles */
.assessment-result {
    max-width: 600px;
    margin: 0 auto;
}

.result-card {
    background: var(--white);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--box-shadow);
    border-left: 4px solid var(--primary);
}

.result-title {
    color: var(--dark);
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.result-description {
    color: var(--gray);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.result-recommendations h5 {
    color: var(--dark);
    font-weight: 600;
    margin-bottom: 1rem;
}

.recommendations-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.recommendations-list li {
    color: var(--gray);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: flex-start;
}

.recommendations-list li i {
    color: var(--success);
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--box-shadow);
}

.card-title {
    color: var(--dark);
    font-weight: 600;
}

.form-label {
    color: var(--dark);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-control {
    border: 2px solid var(--gray-light);
    border-radius: 10px;
    padding: 1rem;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(255, 140, 56, 0.25);
}

.consultation-benefits {
    background: var(--primary-soft);
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid var(--primary);
}

.consultation-benefits h5 {
    color: var(--dark);
    font-weight: 600;
    margin-bottom: 1rem;
}

.benefits-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.benefits-list li {
    padding: 0.5rem 0;
    color: var(--gray);
    font-weight: 500;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    padding: 1rem 2.5rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-hover);
}

.contact-info {
    background: var(--secondary);
    padding: 1rem;
    border-radius: 10px;
}

/* Responsive Design */
@media (max-width: 992px) {
    .schedule-container {
        padding-top: 100px; /* Slightly less padding on tablets */
    }
}

@media (max-width: 768px) {
    .schedule-container {
        padding-top: 90px; /* Less padding on mobile */
        padding-bottom: 2rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .success-icon {
        font-size: 3rem;
    }

    .score-circle {
        width: 100px;
        height: 100px;
    }

    .score-number {
        font-size: 1.5rem;
    }

    .consultation-benefits {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .schedule-container {
        padding-top: 80px; /* Even less padding on small mobile */
    }

    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const gauges = document.querySelectorAll('.score-gauge');

    gauges.forEach(gauge => {
        const score = parseInt(gauge.dataset.score);
        const maxValue = parseInt(gauge.dataset.max);
        const shouldAnimate = gauge.dataset.animate === 'true';
        const gaugeFill = gauge.querySelector('.gauge-fill');

        if (gaugeFill && shouldAnimate) {
            // Calculate the percentage for the gauge fill
            const percentage = Math.min((score / maxValue) * 100, 100);

            // Set CSS custom property for animation
            gaugeFill.style.setProperty('--fill-percentage', percentage + '%');

            // Start animation after a short delay
            setTimeout(() => {
                gaugeFill.style.clipPath = `polygon(0 0, ${percentage}% 0, ${percentage}% 100%, 0 100%)`;
            }, 500);
        } else if (gaugeFill) {
            // Static display without animation
            const percentage = Math.min((score / maxValue) * 100, 100);
            gaugeFill.style.clipPath = `polygon(0 0, ${percentage}% 0, ${percentage}% 100%, 0 100%)`;
        }
    });
});
</script>
@endsection
