@extends('layouts.app')

@section('title', $quiz->title . ' - ' . $landingPage->title)

@section('content')
<div class="quiz-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="quiz-header text-center mb-5">
                    <h1 class="quiz-title">{{ $quiz->title }}</h1>
                    @if($quiz->description)
                        <p class="lead">{{ $quiz->description }}</p>
                    @endif
                    <div class="progress-container">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <small class="text-muted">Question <span id="current-question">1</span> of {{ $quiz->questions->count() }}</small>
                    </div>
                </div>

                <form id="quiz-form" method="POST" action="{{ route('landing-pages.quiz.submit', $landingPage) }}">
                    @csrf

                    <!-- Lead Information Section -->
                    <div class="quiz-section" id="lead-info-section">
                        <div class="card">
                            <div class="card-body">
                                <h3 class="card-title">Your Information</h3>
                                <p class="text-muted mb-4">Please provide your contact information to receive your personalized report.</p>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Full Name *</label>
                                        <input type="text" class="form-control" id="name" name="name" required value="{{ old('name') }}">
                                        @error('name')
                                            <div class="text-danger small">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address *</label>
                                        <input type="email" class="form-control" id="email" name="email" required value="{{ old('email') }}">
                                        @error('email')
                                            <div class="text-danger small">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="company_name" class="form-label">Company Name *</label>
                                        <input type="text" class="form-control" id="company_name" name="company_name" required value="{{ old('company_name') }}">
                                        @error('company_name')
                                            <div class="text-danger small">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone_number" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="phone_number" name="phone_number" value="{{ old('phone_number') }}">
                                        @error('phone_number')
                                            <div class="text-danger small">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quiz Questions -->
                    @foreach($quiz->questions as $index => $question)
                        <div class="quiz-section quiz-question"
                             id="question-{{ $index + 1 }}"
                             style="display: none;"
                             data-question-id="{{ $question->id }}"
                             data-question-type="{{ $question->type }}"
                             data-is-required="{{ $question->is_required ? 'true' : 'false' }}">
                            <div class="card">
                                <div class="card-body">
                                    @if($question->category)
                                        <div class="question-category mb-3">
                                            <span class="badge category-badge" style="background-color: {{ $question->category->color }}; color: white;">
                                                <i class="fas fa-folder me-1"></i>{{ $question->category->name }}
                                            </span>
                                            @if($question->category->description)
                                                <small class="text-muted d-block mt-1">{{ $question->category->description }}</small>
                                            @endif
                                        </div>
                                    @endif
                                    <h3 class="card-title">
                                        {{ $question->question }}
                                        @if($question->is_required)
                                            <span class="text-danger">*</span>
                                        @endif
                                    </h3>

                                    @if($question->type === 'multiple_choice')
                                        @foreach($question->getFormattedOptions() as $key => $option)
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox"
                                                       name="quiz_responses[{{ $question->id }}][]"
                                                       value="{{ $key }}"
                                                       id="q{{ $question->id }}_{{ $key }}">
                                                <label class="form-check-label" for="q{{ $question->id }}_{{ $key }}">
                                                    {{ $option['text'] }}
                                                </label>
                                            </div>
                                        @endforeach

                                    @elseif($question->type === 'single_choice')
                                        @foreach($question->getFormattedOptions() as $key => $option)
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="radio"
                                                       name="quiz_responses[{{ $question->id }}]"
                                                       value="{{ $key }}"
                                                       id="q{{ $question->id }}_{{ $key }}">
                                                <label class="form-check-label" for="q{{ $question->id }}_{{ $key }}">
                                                    {{ $option['text'] }}
                                                </label>
                                            </div>
                                        @endforeach

                                    @elseif($question->type === 'scale')
                                        <div class="scale-container">
                                            <div class="scale-labels d-flex justify-content-between mb-2">
                                                <small class="text-muted">1 - Poor</small>
                                                <small class="text-muted">10 - Excellent</small>
                                            </div>
                                            <div class="scale-options d-flex justify-content-between">
                                                @for($i = 1; $i <= 10; $i++)
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio"
                                                               name="quiz_responses[{{ $question->id }}]"
                                                               value="{{ $i }}"
                                                               id="q{{ $question->id }}_{{ $i }}">
                                                        <label class="form-check-label" for="q{{ $question->id }}_{{ $i }}">
                                                            {{ $i }}
                                                        </label>
                                                    </div>
                                                @endfor
                                            </div>
                                        </div>

                                    @elseif($question->type === 'text')
                                        <textarea class="form-control"
                                                  name="quiz_responses[{{ $question->id }}]"
                                                  rows="4"
                                                  placeholder="Please provide your answer..."></textarea>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach

                    <!-- Navigation Buttons -->
                    <div class="quiz-navigation d-flex justify-content-between mt-4">
                        <button type="button" class="btn btn-outline-secondary" id="prev-btn" style="display: none;">
                            <i class="fas fa-arrow-left me-2"></i>Previous
                        </button>
                        <button type="button" class="btn btn-primary" id="next-btn">
                            Next<i class="fas fa-arrow-right ms-2"></i>
                        </button>
                        <button type="submit" class="btn btn-success" id="submit-btn" style="display: none;">
                            <i class="fas fa-check me-2"></i>Complete Assessment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.quiz-container {
    padding-top: 120px; /* Account for fixed navbar */
    padding-bottom: 3rem;
    min-height: 80vh;
}

.quiz-header h1.quiz-title {
    color: var(--dark);
    font-weight: 700;
    margin-top: 1rem; /* Reduced from 3rem */
    margin-bottom: 2rem; /* Reduced from 3rem */
    padding: 1rem 0; /* Reduced from 2rem 0 */
}

.progress-container {
    max-width: 400px;
    margin: 2rem auto;
}

.progress {
    height: 8px;
    border-radius: 10px;
    background-color: var(--gray-light);
}

.progress-bar {
    background: var(--gradient-primary);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.card-title {
    color: var(--dark);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.form-check {
    padding: 1rem;
    border-radius: 10px;
    transition: background-color 0.2s ease;
}

.form-check:hover {
    background-color: var(--primary-soft);
}

.form-check-input:checked + .form-check-label {
    color: var(--primary);
    font-weight: 600;
}

.question-category {
    margin-bottom: 1rem;
}

.category-badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

.scale-container {
    padding: 1rem;
}

.scale-options .form-check {
    flex-direction: column;
    align-items: center;
    padding: 0.5rem;
    margin: 0;
}

.btn-primary, .btn-success {
    background: var(--gradient-primary);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-primary:hover, .btn-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-hover);
}

.btn-outline-secondary {
    border: 2px solid var(--gray-light);
    color: var(--gray);
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background-color: var(--gray);
    border-color: var(--gray);
    color: white;
}

/* Responsive Design */
@media (max-width: 992px) {
    .quiz-container {
        padding-top: 100px; /* Slightly less padding on tablets */
    }
}

@media (max-width: 768px) {
    .quiz-container {
        padding-top: 90px; /* Less padding on mobile */
        padding-bottom: 2rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .quiz-header h1.quiz-title {
        margin-top: 0.5rem;
        margin-bottom: 1.5rem;
        padding: 0.5rem 0;
        font-size: 1.75rem;
    }

    .scale-options {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .scale-options .form-check {
        flex: 0 0 18%;
    }
}

@media (max-width: 576px) {
    .quiz-container {
        padding-top: 80px; /* Even less padding on small mobile */
    }

    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .quiz-header h1.quiz-title {
        font-size: 1.5rem;
    }
}

/* Validation Error Styles */
.field-error {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.section-error {
    border-radius: 10px;
    border: none;
    background-color: #f8d7da;
    color: #721c24;
    padding: 1rem;
    margin-top: 1rem;
}

.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.form-check-input.is-invalid {
    border-color: #dc3545;
}

.form-check-input.is-invalid:checked {
    background-color: #dc3545;
    border-color: #dc3545;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sections = document.querySelectorAll('.quiz-section');
    const totalSections = sections.length;
    let currentSection = 0;

    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    const submitBtn = document.getElementById('submit-btn');
    const progressBar = document.querySelector('.progress-bar');
    const currentQuestionSpan = document.getElementById('current-question');

    function showSection(index) {
        sections.forEach((section, i) => {
            section.style.display = i === index ? 'block' : 'none';
        });

        // Update progress
        const progress = ((index + 1) / totalSections) * 100;
        progressBar.style.width = progress + '%';
        currentQuestionSpan.textContent = index + 1;

        // Update buttons
        prevBtn.style.display = index > 0 ? 'inline-block' : 'none';
        nextBtn.style.display = index < totalSections - 1 ? 'inline-block' : 'none';
        submitBtn.style.display = index === totalSections - 1 ? 'inline-block' : 'none';
    }

    function validateCurrentSection() {
        const currentSectionEl = sections[currentSection];

        // Clear any previous error messages
        clearErrorMessages(currentSectionEl);

        // Validate required inputs (lead information)
        const requiredInputs = currentSectionEl.querySelectorAll('input[required], textarea[required]');
        for (let input of requiredInputs) {
            if (!input.value.trim()) {
                showFieldError(input, 'This field is required.');
                input.focus();
                return false;
            }
        }

        // Additional validation for specific input types
        const emailInputs = currentSectionEl.querySelectorAll('input[type="email"]');
        for (let emailInput of emailInputs) {
            if (emailInput.value.trim() && !isValidEmail(emailInput.value.trim())) {
                showFieldError(emailInput, 'Please enter a valid email address.');
                emailInput.focus();
                return false;
            }
        }

        // Validate phone numbers (optional but if provided should be reasonable)
        const phoneInputs = currentSectionEl.querySelectorAll('input[type="tel"]');
        for (let phoneInput of phoneInputs) {
            const phoneValue = phoneInput.value.trim();
            if (phoneValue && !isValidPhone(phoneValue)) {
                showFieldError(phoneInput, 'Please enter a valid phone number.');
                phoneInput.focus();
                return false;
            }
        }

        // Validate quiz questions if this is a quiz section
        if (currentSectionEl.classList.contains('quiz-question')) {
            return validateQuizQuestion(currentSectionEl);
        }

        return true;
    }

    function validateQuizQuestion(sectionEl) {
        const questionId = sectionEl.dataset.questionId;
        const questionType = sectionEl.dataset.questionType;
        const isRequired = sectionEl.dataset.isRequired === 'true';

        let isValid = true;
        let errorMessage = '';

        switch (questionType) {
            case 'multiple_choice':
                const checkboxes = sectionEl.querySelectorAll('input[type="checkbox"]');
                const checkedBoxes = sectionEl.querySelectorAll('input[type="checkbox"]:checked');

                if (isRequired && checkedBoxes.length === 0) {
                    errorMessage = 'Please select at least one option.';
                    isValid = false;
                }
                break;

            case 'single_choice':
                const radios = sectionEl.querySelectorAll('input[type="radio"]');
                const checkedRadio = sectionEl.querySelector('input[type="radio"]:checked');

                if (isRequired && !checkedRadio) {
                    errorMessage = 'Please select an option.';
                    isValid = false;
                }
                break;

            case 'text':
                const textInput = sectionEl.querySelector('textarea, input[type="text"]');
                const textValue = textInput ? textInput.value.trim() : '';

                if (isRequired && textValue === '') {
                    errorMessage = 'Please provide a response.';
                    isValid = false;
                } else if (textValue.length > 1000) {
                    errorMessage = 'Response is too long (maximum 1000 characters).';
                    isValid = false;
                } else if (textValue.length > 0 && textValue.length < 2) {
                    errorMessage = 'Response is too short (minimum 2 characters).';
                    isValid = false;
                }
                break;

            case 'scale':
                const scaleRadios = sectionEl.querySelectorAll('input[type="radio"]');
                const checkedScaleRadio = sectionEl.querySelector('input[type="radio"]:checked');
                const scaleValue = checkedScaleRadio ? parseInt(checkedScaleRadio.value) : null;

                if (isRequired && !checkedScaleRadio) {
                    errorMessage = 'Please select a rating.';
                    isValid = false;
                } else if (scaleValue !== null && (scaleValue < 1 || scaleValue > 10)) {
                    errorMessage = 'Please select a value between 1 and 10.';
                    isValid = false;
                }
                break;
        }

        if (!isValid) {
            showSectionError(sectionEl, errorMessage);
        }

        return isValid;
    }

    function showFieldError(input, message) {
        // Remove existing error
        const existingError = input.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        // Add error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error text-danger small mt-1';
        errorDiv.textContent = message;
        input.parentNode.appendChild(errorDiv);

        // Add error styling to input
        input.classList.add('is-invalid');
    }

    function showSectionError(section, message) {
        // Remove existing error
        const existingError = section.querySelector('.section-error');
        if (existingError) {
            existingError.remove();
        }

        // Add error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'section-error alert alert-danger mt-3';
        errorDiv.textContent = message;
        section.appendChild(errorDiv);
    }

    function clearErrorMessages(section) {
        // Remove field errors
        const fieldErrors = section.querySelectorAll('.field-error');
        fieldErrors.forEach(error => error.remove());

        // Remove section errors
        const sectionErrors = section.querySelectorAll('.section-error');
        sectionErrors.forEach(error => error.remove());

        // Remove error styling
        const invalidInputs = section.querySelectorAll('.is-invalid');
        invalidInputs.forEach(input => input.classList.remove('is-invalid'));
    }

    function isValidEmail(email) {
        // Regular expression for email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function isValidPhone(phone) {
        // Remove all non-digit characters for validation
        const cleanPhone = phone.replace(/\D/g, '');

        // Check if it's a reasonable length (7-15 digits)
        // This covers most international phone number formats
        return cleanPhone.length >= 7 && cleanPhone.length <= 15;
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            if (validateCurrentSection() && currentSection < totalSections - 1) {
                currentSection++;
                showSection(currentSection);
            }
        });
    }

    if (prevBtn) {
        prevBtn.addEventListener('click', function() {
            if (currentSection > 0) {
                currentSection--;
                showSection(currentSection);
            }
        });
    }

    // Handle form submission
    const form = document.getElementById('quiz-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            // Validate the current (last) section before submitting
            if (!validateCurrentSection()) {
                e.preventDefault();
                return false;
            }

            // Show loading state
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
            }
        });
    }

    // Also add click handler for submit button as backup
    if (submitBtn) {
        submitBtn.addEventListener('click', function(e) {
            // If this is a type="submit" button, let it submit naturally
            // But if for some reason the form submission isn't working, force it
            setTimeout(function() {
                if (validateCurrentSection() && form) {
                    form.submit();
                }
            }, 100);
        });
    }

    // Initialize
    showSection(0);
});
</script>
@endsection

@section('additional_head')
<meta name="csrf-token" content="{{ csrf_token() }}">
@endsection
