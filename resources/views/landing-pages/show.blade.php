@extends('layouts.app')

@section('title', $landingPage->meta_title ?: $landingPage->title)
@section('meta_description', $landingPage->meta_description)

@section('content')
<div class="landing-page-container">
    <!-- Landing Page Content -->
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="landing-content">
                    {!! $landingPage->content !!}
                </div>

                @if($landingPage->has_quiz)
                    <div class="text-center mt-5">
                        <a href="{{ route('landing-pages.quiz', $landingPage) }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-play-circle me-2"></i>
                            Start Assessment
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
.landing-page-container {
    padding-top: 120px; /* Account for fixed navbar */
    padding-bottom: 3rem;
    min-height: 80vh;
}

.landing-content {
    background: white;
    padding: 3rem;
    border-radius: 15px;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
    margin-top: 1rem; /* Add top margin for better spacing */
}

.landing-content h1 {
    color: var(--dark);
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.landing-content h2 {
    color: var(--dark);
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.landing-content h3 {
    color: var(--dark);
    font-weight: 600;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.landing-content p {
    color: var(--gray);
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.landing-content ul, .landing-content ol {
    color: var(--gray);
    line-height: 1.8;
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.landing-content li {
    margin-bottom: 0.5rem;
}

.landing-content img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    margin: 1.5rem 0;
}

.landing-content blockquote {
    background: var(--primary-soft);
    border-left: 4px solid var(--primary);
    padding: 1.5rem;
    margin: 2rem 0;
    border-radius: 0 10px 10px 0;
    font-style: italic;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    padding: 1rem 2.5rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-hover);
}

/* Responsive Design */
@media (max-width: 992px) {
    .landing-page-container {
        padding-top: 100px; /* Slightly less padding on tablets */
    }

    .landing-content {
        padding: 2.5rem 2rem;
    }
}

@media (max-width: 768px) {
    .landing-page-container {
        padding-top: 90px; /* Less padding on mobile */
        padding-bottom: 2rem;
    }

    .landing-content {
        padding: 2rem 1.5rem;
        margin-top: 0.5rem;
    }
}

@media (max-width: 576px) {
    .landing-page-container {
        padding-top: 80px; /* Even less padding on small mobile */
    }

    .landing-content {
        padding: 1.5rem 1rem;
        border-radius: 10px;
    }

    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}
</style>
@endsection
