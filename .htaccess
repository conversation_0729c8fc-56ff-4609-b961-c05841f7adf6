<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Redirect non-www to www (except for localhost and IP addresses)
    # This rule must come before any other rules
    RewriteCond %{HTTP_HOST} ^endpointsync\.com$ [NC]
    RewriteRule ^(.*)$ http://www.endpointsync.com/$1 [R=301,L]
    
    # Generic rule for any other domains without www
    RewriteCond %{HTTP_HOST} !^www\. [NC]
    RewriteCond %{HTTP_HOST} !^localhost [NC]
    RewriteCond %{HTTP_HOST} !^127\. [NC]
    RewriteCond %{HTTP_HOST} !^10\. [NC]
    RewriteCond %{HTTP_HOST} !^172\.(1[6-9]|2[0-9]|3[0-1])\. [NC]
    RewriteCond %{HTTP_HOST} !^192\.168\. [NC]
    RewriteCond %{HTTP_HOST} !\d+\.\d+\.\d+\.\d+ [NC]
    RewriteCond %{HTTPS}s ^on(s)|
    RewriteRule ^ http%1://www.%{HTTP_HOST}%{REQUEST_URI} [R=301,L]
    
    # Send all requests to the public folder
    RewriteCond %{REQUEST_URI} !^/public/
    RewriteRule ^(.*)$ public/$1 [L]
</IfModule>
