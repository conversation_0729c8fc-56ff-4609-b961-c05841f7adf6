<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Special rule for sitemap.xml - serve directly if it exists
    RewriteCond %{REQUEST_URI} ^/sitemap\.xml$ [NC]
    RewriteCond %{DOCUMENT_ROOT}/sitemap.xml -f
    RewriteRule ^sitemap\.xml$ sitemap.xml [L]

    # Redirect non-www to www (except for localhost and IP addresses)
    # This rule must come before any other rules
    RewriteCond %{HTTP_HOST} ^endpointsync\.com$ [NC]
    RewriteCond %{REQUEST_URI} !^/sitemap\.xml$ [NC]
    RewriteRule ^(.*)$ http://www.endpointsync.com/$1 [R=301,L]

    # Generic rule for any other domains without www
    RewriteCond %{HTTP_HOST} !^www\. [NC]
    RewriteCond %{HTTP_HOST} !^localhost [NC]
    RewriteCond %{HTTP_HOST} !^127\. [NC]
    RewriteCond %{HTTP_HOST} !^10\. [NC]
    RewriteCond %{HTTP_HOST} !^172\.(1[6-9]|2[0-9]|3[0-1])\. [NC]
    RewriteCond %{HTTP_HOST} !^192\.168\. [NC]
    RewriteCond %{HTTP_HOST} !\d+\.\d+\.\d+\.\d+ [NC]
    RewriteCond %{REQUEST_URI} !^/sitemap\.xml$ [NC]
    RewriteCond %{HTTPS}s ^on(s)|
    RewriteRule ^ http%1://www.%{HTTP_HOST}%{REQUEST_URI} [R=301,L]

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Handle X-XSRF-Token Header
    RewriteCond %{HTTP:x-xsrf-token} .
    RewriteRule .* - [E=HTTP_X_XSRF_TOKEN:%{HTTP:X-XSRF-Token}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
