/*
* EndpointSync Admin Styles
* Modern, professional styling with Bootstrap 5 integration
* Blue and gray color scheme with orange highlights
*/

/* Admin color variables */
:root {
    /* Blue and Gray Color Palette with Orange Highlights */
    --admin-primary: #3b82f6; /* Blue primary color */
    --admin-primary-light: #60a5fa; /* Lighter blue */
    --admin-primary-dark: #2563eb; /* Darker blue */
    --admin-primary-soft: rgba(59, 130, 246, 0.08); /* Soft blue for backgrounds */

    /* Orange highlight colors */
    --admin-highlight: #ff8c38; /* Orange highlight */
    --admin-highlight-light: #ffa563; /* Lighter orange */
    --admin-highlight-dark: #e67321; /* Darker orange */
    --admin-highlight-soft: rgba(255, 140, 56, 0.08); /* Soft orange for backgrounds */

    /* Gray scale */
    --admin-gray-100: #f3f4f6; /* Lightest gray */
    --admin-gray-200: #e5e7eb; /* Light gray */
    --admin-gray-300: #d1d5db; /* Medium light gray */
    --admin-gray-400: #9ca3af; /* Medium gray */
    --admin-gray-500: #6b7280; /* Medium dark gray */
    --admin-gray-600: #4b5563; /* Dark gray */
    --admin-gray-700: #374151; /* Darker gray */
    --admin-gray-800: #1f2937; /* Very dark gray */
    --admin-gray-900: #111827; /* Almost black */

    /* Other colors */
    --admin-white: #ffffff;
    --admin-light: #f9fafb;
    --admin-success: #10b981; /* Green */
    --admin-info: #3b82f6; /* Blue */
    --admin-warning: #f59e0b; /* Amber */
    --admin-danger: #ef4444; /* Red */

    /* Gradients and effects */
    --admin-gradient-blue: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
    --admin-gradient-orange: linear-gradient(135deg, var(--admin-highlight) 0%, var(--admin-highlight-light) 100%);
    --admin-box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
    --admin-box-shadow-hover: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --admin-transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* General Admin Styles */
.admin-body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--admin-gray-100);
    color: var(--admin-gray-800);
}

/* Admin Sidebar */
#sidebar {
    min-height: 100vh;
    transition: var(--admin-transition);
    z-index: 1000;
    background-color: var(--admin-white);
    border-right: 1px solid var(--admin-gray-200);
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.03);
}

.admin-nav-link {
    color: var(--admin-gray-700);
    padding: 0.75rem 1rem;
    transition: var(--admin-transition);
    font-weight: 500;
    border-radius: 0.375rem;
    margin-bottom: 0.25rem;
}

.admin-nav-link:hover {
    background-color: var(--admin-primary-soft);
    color: var(--admin-primary);
    transform: translateX(5px);
}

.admin-nav-link.active {
    background-color: var(--admin-primary-soft);
    color: var(--admin-primary);
    font-weight: 600;
    border-left: 3px solid var(--admin-primary);
}

/* Logo styling */
.logo-endpoint {
    color: var(--admin-primary);
    font-weight: 700;
}

.logo-sync {
    color: var(--admin-gray-700);
    font-weight: 600;
}

/* Content Wrapper */
.content-wrapper {
    border-radius: 0.5rem;
    transition: var(--admin-transition);
    background-color: var(--admin-white);
    box-shadow: var(--admin-box-shadow);
    border: 1px solid var(--admin-gray-200);
}

/* Admin Card Styling */
.admin-card, .card {
    border-radius: 0.5rem;
    box-shadow: var(--admin-box-shadow);
    transition: var(--admin-transition);
    border: 1px solid var(--admin-gray-200);
    overflow: hidden;
    height: 100%;
    background-color: var(--admin-white);
}

.admin-card:hover, .card:hover {
    box-shadow: var(--admin-box-shadow-hover);
    transform: translateY(-3px);
}

.admin-card .card-header, .card .card-header {
    background-color: var(--admin-white);
    border-bottom: 1px solid var(--admin-gray-200);
    padding: 1rem 1.25rem;
}

.admin-card .card-footer, .card .card-footer {
    background-color: var(--admin-white);
    border-top: 1px solid var(--admin-gray-200);
    padding: 1rem 1.25rem;
}

/* Card with blue header */
.card-blue-header .card-header {
    background-color: var(--admin-primary);
    color: var(--admin-white);
    border-bottom: none;
}

/* Button Styling */
.btn {
    font-weight: 500;
    border-radius: 0.375rem;
    padding: 0.5rem 1.25rem;
    transition: var(--admin-transition);
}

/* Primary button - Blue */
.btn-primary {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
    color: var(--admin-white);
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
    background-color: var(--admin-primary-dark);
    border-color: var(--admin-primary-dark);
    color: var(--admin-white);
    box-shadow: 0 4px 10px rgba(37, 99, 235, 0.3);
}

.btn-outline-primary {
    color: var(--admin-primary);
    border-color: var(--admin-primary);
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary:active {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
    color: var(--admin-white);
    box-shadow: 0 4px 10px rgba(37, 99, 235, 0.2);
}

/* Highlight button - Orange */
.btn-highlight {
    background-color: var(--admin-highlight);
    border-color: var(--admin-highlight);
    color: var(--admin-white);
}

.btn-highlight:hover,
.btn-highlight:focus,
.btn-highlight:active {
    background-color: var(--admin-highlight-dark);
    border-color: var(--admin-highlight-dark);
    color: var(--admin-white);
    box-shadow: 0 4px 10px rgba(230, 115, 33, 0.3);
}

.btn-outline-highlight {
    color: var(--admin-highlight);
    border-color: var(--admin-highlight);
}

.btn-outline-highlight:hover,
.btn-outline-highlight:focus,
.btn-outline-highlight:active {
    background-color: var(--admin-highlight);
    border-color: var(--admin-highlight);
    color: var(--admin-white);
    box-shadow: 0 4px 10px rgba(230, 115, 33, 0.2);
}

/* Button sizes */
.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

/* Table Styling */
.table {
    margin-bottom: 0;
    border-color: var(--admin-gray-200);
}

.table th {
    font-weight: 600;
    color: var(--admin-gray-800);
    border-bottom-width: 1px;
    background-color: var(--admin-gray-100);
    padding: 0.75rem 1rem;
}

.table td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
    border-color: var(--admin-gray-200);
}

.table-hover tbody tr:hover {
    background-color: var(--admin-primary-soft);
}

/* Striped table */
.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: var(--admin-gray-100);
}

/* Badge Styling */
.badge {
    font-weight: 600;
    padding: 0.35em 0.65em;
    border-radius: 0.25rem;
}

/* Blue badges */
.bg-primary {
    background-color: var(--admin-primary) !important;
}

.bg-primary-soft {
    background-color: var(--admin-primary-soft);
}

.text-primary {
    color: var(--admin-primary) !important;
}

/* Orange highlight badges */
.bg-highlight {
    background-color: var(--admin-highlight) !important;
}

.bg-highlight-soft {
    background-color: var(--admin-highlight-soft);
}

.text-highlight {
    color: var(--admin-highlight) !important;
}

/* Gray badges */
.bg-gray-100 {
    background-color: var(--admin-gray-100) !important;
}

.bg-gray-200 {
    background-color: var(--admin-gray-200) !important;
}

.text-gray {
    color: var(--admin-gray-500) !important;
}

/* Form Controls */
.form-control:focus,
.form-select:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--admin-gray-700);
}

.form-text {
    color: var(--admin-gray-500);
}

/* Form with blue focus */
.form-control-blue:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
}

/* Form with orange focus (for highlight fields) */
.form-control-highlight:focus {
    border-color: var(--admin-highlight);
    box-shadow: 0 0 0 0.25rem rgba(255, 140, 56, 0.25);
}

/* Pagination */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: var(--admin-primary);
    border: 1px solid var(--admin-gray-200);
    padding: 0.5rem 0.75rem;
}

.page-link:hover {
    background-color: var(--admin-primary-soft);
    color: var(--admin-primary);
    border-color: var(--admin-gray-200);
}

.page-item.active .page-link {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
}

/* Orange pagination variant */
.pagination-highlight .page-link {
    color: var(--admin-highlight);
}

.pagination-highlight .page-link:hover {
    background-color: var(--admin-highlight-soft);
    color: var(--admin-highlight);
}

.pagination-highlight .page-item.active .page-link {
    background-color: var(--admin-highlight);
    border-color: var(--admin-highlight);
}

/* Alert Styling */
.alert {
    border-radius: 0.375rem;
    padding: 1rem;
    border: none;
    box-shadow: var(--admin-box-shadow);
}

.alert-primary {
    background-color: var(--admin-primary-soft);
    color: var(--admin-primary-dark);
}

.alert-highlight {
    background-color: var(--admin-highlight-soft);
    color: var(--admin-highlight-dark);
}

/* Dropdown Styling */
.dropdown-menu {
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border: 1px solid var(--admin-gray-200);
    padding: 0.5rem 0;
    background-color: var(--admin-white);
}

.dropdown-item {
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    color: var(--admin-gray-700);
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: var(--admin-primary-soft);
    color: var(--admin-primary);
}

.dropdown-item.highlight:hover,
.dropdown-item.highlight:focus {
    background-color: var(--admin-highlight-soft);
    color: var(--admin-highlight);
}

/* Utility Classes */
.shadow-sm {
    box-shadow: var(--admin-box-shadow) !important;
}

.shadow-md {
    box-shadow: var(--admin-box-shadow-hover) !important;
}

.rounded {
    border-radius: 0.375rem !important;
}

.rounded-lg {
    border-radius: 0.5rem !important;
}

.border-blue {
    border-color: var(--admin-primary) !important;
}

.border-orange {
    border-color: var(--admin-highlight) !important;
}

.bg-blue-soft {
    background-color: var(--admin-primary-soft) !important;
}

.bg-orange-soft {
    background-color: var(--admin-highlight-soft) !important;
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
    #sidebar {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1050;
    }

    .content-wrapper {
        padding: 1rem !important;
    }

    .table-responsive {
        border-radius: 0.375rem;
    }
}
