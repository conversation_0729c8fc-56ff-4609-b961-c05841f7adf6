/**
 * EndpointSync Website JavaScript
 * Handles parallax effects, animations, and interactive features
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS animations
    initAOS();

    // Initialize Bootstrap components
    initBootstrapComponents();

    // Initialize parallax effects
    initParallax();

    // Smooth scrolling for anchor links
    initSmoothScroll();

    // Navbar scroll behavior
    initNavbarScroll();

    // Form submission handling
    initFormHandling();

    // Initialize image hover effects
    initImageEffects();
});

/**
 * Initialize AOS (Animate On Scroll) library
 */
function initAOS() {
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            mirror: false,
            offset: 50
        });
    }
}

/**
 * Initialize Bootstrap components
 */
function initBootstrapComponents() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

/**
 * Initialize parallax effects
 */
function initParallax() {
    // Apply parallax effect to sections with background images
    const parallaxSections = document.querySelectorAll('.parallax-section');

    if (parallaxSections.length > 0 && typeof simpleParallax !== 'undefined') {
        // Simple background parallax effect using CSS
        window.addEventListener('scroll', function() {
            const scrollPosition = window.pageYOffset;

            parallaxSections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.offsetHeight;

                // Calculate parallax offset
                if (scrollPosition + window.innerHeight > sectionTop &&
                    scrollPosition < sectionTop + sectionHeight) {
                    const yPos = (scrollPosition - sectionTop) * 0.3;
                    section.style.backgroundPosition = `center ${yPos}px`;
                }
            });
        });
    }
}

/**
 * Initialize image hover effects
 */
function initImageEffects() {
    // Add hover effects to images
    const images = document.querySelectorAll('.img-fluid.rounded-3.shadow-lg');

    images.forEach(img => {
        img.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
            this.style.boxShadow = '0 20px 30px rgba(0, 0, 0, 0.15)';
        });

        img.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.boxShadow = '';
        });
    });
}

/**
 * Initialize smooth scrolling for anchor links
 */
function initSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            if (targetId === '#') return;

            const targetElement = document.querySelector(targetId);
            if (!targetElement) return;

            const navbarHeight = document.querySelector('.navbar').offsetHeight;
            const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - navbarHeight;

            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });

            // Close mobile menu if open
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('.navbar-collapse');
            if (navbarCollapse.classList.contains('show')) {
                navbarToggler.click();
            }
        });
    });
}

/**
 * Initialize navbar scroll behavior
 */
function initNavbarScroll() {
    const navbar = document.querySelector('.navbar');

    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('navbar-scrolled');
            navbar.style.padding = '0.5rem 0';
        } else {
            navbar.classList.remove('navbar-scrolled');
            navbar.style.padding = '1rem 0';
        }
    });
}

/**
 * Initialize form handling
 */
function initFormHandling() {
    // Initialize consultation modal
    initConsultationModal();

    const contactForm = document.getElementById('contactForm');

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Here you would typically send the form data to your backend
            // For demo purposes, we'll just show a success message

            // Get form data
            const formData = new FormData(contactForm);
            const formDataObj = {};
            formData.forEach((value, key) => {
                formDataObj[key] = value;
            });

            // Log form data (for demo purposes)
            console.log('Form submitted:', formDataObj);

            // Show success message
            const formContainer = contactForm.parentElement;
            formContainer.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-check-circle text-success fa-4x mb-4"></i>
                    <h3 class="h4 mb-3">Thank You!</h3>
                    <p class="mb-4">Your consultation request has been received. One of our team members will contact you shortly to schedule your session.</p>
                    <button type="button" class="btn btn-primary rounded-pill" onclick="location.reload()">Submit Another Request</button>
                </div>
            `;
        });
    }
}

/**
 * Initialize consultation modal and form handling
 */
function initConsultationModal() {
    // Add event listeners to all "Book a Free Consultation" buttons
    document.querySelectorAll('.consultation-btn').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Show the modal
            const consultationModal = new bootstrap.Modal(document.getElementById('consultationModal'));
            consultationModal.show();

            // Reset form when modal is opened
            const consultationForm = document.getElementById('consultationForm');
            if (consultationForm) {
                consultationForm.reset();
                consultationForm.classList.remove('was-validated');
                document.getElementById('successMessage').classList.add('d-none');
                consultationForm.classList.remove('d-none');
            }
        });
    });

    // Handle consultation form submission
    const consultationForm = document.getElementById('consultationForm');

    if (consultationForm) {
        // Real-time validation for input fields
        const formInputs = consultationForm.querySelectorAll('input, textarea');
        formInputs.forEach(input => {
            input.addEventListener('blur', function() {
                // Check validity when input loses focus
                if (this.checkValidity()) {
                    this.classList.add('is-valid');
                } else if (this.value !== '') {
                    this.classList.add('is-invalid');
                }
            });

            // Remove validation classes when user starts typing again
            input.addEventListener('focus', function() {
                this.classList.remove('is-invalid');
            });
        });

        // Form submission
        consultationForm.addEventListener('submit', function(e) {
            e.preventDefault();

            if (!this.checkValidity()) {
                e.stopPropagation();
                this.classList.add('was-validated');

                // Scroll to the first invalid element
                const firstInvalid = consultationForm.querySelector(':invalid');
                if (firstInvalid) {
                    firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstInvalid.focus();
                }
                return;
            }

            // Show loading state on button
            const submitButton = consultationForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Submitting...';
            submitButton.disabled = true;

            // Get form data
            const formData = new FormData(consultationForm);
            const formDataObj = {};
            formData.forEach((value, key) => {
                formDataObj[key] = value;
            });

            // Send data to backend
            fetch('/api/consultations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify(formDataObj)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Hide the form and show success message with animation
                    consultationForm.classList.add('d-none');
                    const successMessage = document.getElementById('successMessage');
                    successMessage.classList.remove('d-none');

                    // Add animation class to success icon
                    const successIcon = successMessage.querySelector('i.fa-check-circle');
                    if (successIcon) {
                        successIcon.classList.add('animate__animated', 'animate__bounceIn');
                    }

                    // Reset form for future use
                    consultationForm.reset();
                    consultationForm.classList.remove('was-validated');

                    // Reset all inputs to remove validation styling
                    formInputs.forEach(input => {
                        input.classList.remove('is-valid', 'is-invalid');
                    });
                } else {
                    console.error('Form submission error:', data);
                    showFormError('There was an error submitting your request. Please try again.');
                }
            })
            .catch(error => {
                console.error('Form submission error:', error);
                showFormError('There was an error submitting your request. Please try again.');
            })
            .finally(() => {
                // Reset button state
                submitButton.innerHTML = originalButtonText;
                submitButton.disabled = false;
            });
        });

        // Helper function to show form errors
        function showFormError(message) {
            // Create or update error alert
            let errorAlert = document.getElementById('formErrorAlert');
            if (!errorAlert) {
                errorAlert = document.createElement('div');
                errorAlert.id = 'formErrorAlert';
                errorAlert.className = 'alert alert-danger alert-dismissible fade show mt-3';
                errorAlert.innerHTML = `
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="errorMessage"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;
                consultationForm.prepend(errorAlert);
            }

            // Set error message
            document.getElementById('errorMessage').textContent = message;

            // Scroll to error
            errorAlert.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
}
