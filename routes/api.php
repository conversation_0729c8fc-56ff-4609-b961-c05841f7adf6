<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Consultation API routes protected by Sanctum and IP restriction
Route::middleware(['auth:sanctum', 'ip.restrict'])->prefix('v1')->group(function () {
    // Consultation routes
    Route::apiResource('consultations', \App\Http\Controllers\Api\ConsultationApiController::class);
    Route::patch('consultations/{consultation}/mark-as-contacted', [\App\Http\Controllers\Api\ConsultationApiController::class, 'markAsContacted'])
        ->name('api.consultations.mark-as-contacted');
    Route::patch('consultations/{consultation}/update-status', [\App\Http\Controllers\Api\ConsultationApiController::class, 'updateStatus'])
        ->name('api.consultations.update-status');

    // Consultation Status routes
    Route::get('consultation-statuses', function() {
        return response()->json([
            'success' => true,
            'data' => \App\Models\ConsultationStatus::orderBy('sort_order')->get()
        ]);
    })->name('api.consultation-statuses.index');
});
