<?php

use App\Http\Controllers\Admin\ApiSettingsController;
use App\Http\Controllers\Admin\ApiTokenController;
use Illuminate\Support\Facades\Route;

// These routes are included in the admin middleware group in web.php
// API Settings
Route::get('settings/api', [ApiSettingsController::class, 'index'])->name('settings.api');
Route::put('settings/api', [ApiSettingsController::class, 'update'])->name('settings.api.update');
// API documentation routes removed as documentation was removed

// API Token Management
Route::get('settings/api-tokens', [ApiTokenController::class, 'index'])->name('settings.api-tokens');
Route::post('settings/api-tokens', [ApiTokenController::class, 'store'])->name('settings.api-tokens.store');
Route::delete('settings/api-tokens/{token}', [ApiTokenController::class, 'destroy'])->name('settings.api-tokens.destroy');
