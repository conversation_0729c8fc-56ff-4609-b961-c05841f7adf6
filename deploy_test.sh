#!/bin/bash

# ======== CONFIGURATION ========
# Server connection details
REMOTE_USER="u467302425"
REMOTE_HOST="*************"
REMOTE_PORT=65002

# Project paths
LOCAL_PROJECT_PATH="/home/<USER>/dev/endpointsync.com"
REMOTE_PUBLIC_HTML="domains/endpointsync.com/public_html"

# PHP path on Hostinger (PHP 8.4)
REMOTE_PHP="/opt/alt/php84/usr/bin/php"
REMOTE_COMPOSER="/home/<USER>/tools/composer.phar"

# Password storage configuration
PASSWORD_FILE="$HOME/.simpletracker_deploy_password"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ======== HELPER FUNCTIONS ========
# Log messages with timestamp
log_message() {
  local level=$1
  local message=$2
  local color=$GREEN

  case $level in
    "INFO") color=$BLUE ;;
    "WARN") color=$YELLOW ;;
    "ERROR") color=$RED ;;
  esac

  echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] $level: $message${NC}"
}

# Rollback function for failed deployments
function rollback {
  log_message "ERROR" "🔄 Deployment failed. Rolling back..."

  # Restore backed up files
  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
    if [ -f $REMOTE_PUBLIC_HTML/index.php.bak ]; then
      mv $REMOTE_PUBLIC_HTML/index.php.bak $REMOTE_PUBLIC_HTML/index.php
    fi
    if [ -f $REMOTE_PUBLIC_HTML/.htaccess.bak ]; then
      mv $REMOTE_PUBLIC_HTML/.htaccess.bak $REMOTE_PUBLIC_HTML/.htaccess
    fi
  "

  log_message "INFO" "✅ Rollback completed. Previous state restored."
  exit 1
}

# ======== PASSWORD HANDLING ========
# Function to handle SSH password securely
handle_ssh_password() {
  # Check for command line arguments
  if [[ "$1" == "--reset-password" ]]; then
    log_message "INFO" "Resetting stored password..."
    rm -f "$PASSWORD_FILE" 2>/dev/null
  fi

  # Check if password is already stored
  if [[ -f "$PASSWORD_FILE" ]]; then
    SSHPASS=$(cat "$PASSWORD_FILE")
    log_message "INFO" "Using stored password."
    log_message "INFO" "(Use --reset-password to clear stored password)"
  else
    # Prompt for password
    read -s -p "Enter SSH password for $REMOTE_USER@$REMOTE_HOST: " SSHPASS
    echo ""

    # Validate password is not empty
    if [[ -z "$SSHPASS" ]]; then
      log_message "ERROR" "Password cannot be empty."
      exit 1
    fi

    # Store password securely (only readable by the user)
    echo "$SSHPASS" > "$PASSWORD_FILE"
    chmod 600 "$PASSWORD_FILE"
    log_message "INFO" "Password stored securely for future deployments."
  fi
}

# ======== CONNECTION TESTING ========
# Test SSH connection to remote server
test_ssh_connection() {
  log_message "INFO" "🔍 Testing connection to $REMOTE_HOST..."
  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "echo '✅ Connection successful.'" >/dev/null 2>&1

  if [ $? -ne 0 ]; then
    log_message "ERROR" "❌ Connection failed. Please check host/IP or network connectivity."
    exit 1
  fi

  log_message "INFO" "✅ Connection successful."
}

# ======== SERVER ENVIRONMENT CHECK ========
# Check PHP version on remote server
check_php_version() {
  log_message "INFO" "🔍 Checking PHP version on server..."
  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
    SERVER_PHP_VERSION=\$($REMOTE_PHP -r 'echo PHP_VERSION;')
    echo -e \"Server PHP version: \$SERVER_PHP_VERSION\"

    # Extract PHP version requirements from composer.json
    if [ -f $REMOTE_PUBLIC_HTML/composer.json ]; then
      PHP_REQUIREMENT=\$(grep -o '\"php\".*\".*\"' $REMOTE_PUBLIC_HTML/composer.json | grep -o '\"[0-9].*\"' | tr -d '\"')
      if [ ! -z \"\$PHP_REQUIREMENT\" ]; then
        echo -e \"Required PHP version: \$PHP_REQUIREMENT\"
      fi
    fi
  "
}

# Process password
handle_ssh_password "$1"

# Test connection
test_ssh_connection

# Check PHP version
check_php_version

# ======== BUILD FRONTEND ASSETS ========
# Build frontend assets for production
build_frontend_assets() {
  log_message "INFO" "🔨 Building frontend assets..."

  # Save current directory to return to it later
  local current_dir=$(pwd)

  # Change to project directory and build assets
  cd "$LOCAL_PROJECT_PATH" && npm run build

  if [ $? -ne 0 ]; then
    log_message "WARN" "❌ Frontend build failed. Continuing anyway..."
  else
    log_message "INFO" "✅ Frontend assets built successfully."
  fi

  # Return to original directory
  cd "$current_dir"
}

# ======== PREPARE DEPLOYMENT FILES ========
# Create temporary directory with essential files for deployment
prepare_deployment_files() {
  log_message "INFO" "📦 Preparing essential files for upload..."

  # Create temp directory and set cleanup trap
  TEMP_DIR=$(mktemp -d)
  trap 'rm -rf "$TEMP_DIR"' EXIT

  # Define essential directories to create
  local dirs=(
    "app"
    "bootstrap/cache"
    "config"
    "database/migrations"
    "database/seeders"
    "resources"
    "routes"
    "storage/app/public"
    "storage/framework/cache"
    "storage/framework/sessions"
    "storage/framework/views"
    "storage/logs"
    "public"
    "public/build"
    "public/css"
    "public/js"
    "public/assets"
    "public/assets/img"
    "public/assets/img/cards"
    "public/assets/img/sections"
    "public/docs"
  )

  # Create directory structure
  for dir in "${dirs[@]}"; do
    mkdir -p "$TEMP_DIR/$dir"
  done

  log_message "INFO" "Directory structure created."

  # Define essential files and directories to copy
  local copy_list=(
    "app"
    "bootstrap"
    "config"
    "database/migrations"
    "database/seeders"
    "resources"
    "routes"
    "artisan"
    "composer.json"
    "composer.lock"
    "package.json"
  )

  # Optional files that may not exist
  local optional_files=(
    "vite.config.js"
    "tailwind.config.js"
    "postcss.config.js"
  )

  # Copy essential files
  for item in "${copy_list[@]}"; do
    if [ -d "$LOCAL_PROJECT_PATH/$item" ]; then
      cp -r "$LOCAL_PROJECT_PATH/$item/"* "$TEMP_DIR/$item/" 2>/dev/null || true
    elif [ -f "$LOCAL_PROJECT_PATH/$item" ]; then
      cp "$LOCAL_PROJECT_PATH/$item" "$TEMP_DIR/$item"
    fi
  done

  # Copy optional files
  for file in "${optional_files[@]}"; do
    if [ -f "$LOCAL_PROJECT_PATH/$file" ]; then
      cp "$LOCAL_PROJECT_PATH/$file" "$TEMP_DIR/"
    fi
  done

  # Copy public files (excluding build assets)
  log_message "INFO" "Copying CSS, JS, and other static assets..."

  # Copy CSS files (including AdminLTE customizations)
  if [ -d "$LOCAL_PROJECT_PATH/public/css" ]; then
    cp -r "$LOCAL_PROJECT_PATH/public/css/"* "$TEMP_DIR/public/css/" 2>/dev/null || true
    log_message "INFO" "CSS files copied (including AdminLTE customizations)."
  fi

  # Copy JS files
  if [ -d "$LOCAL_PROJECT_PATH/public/js" ]; then
    cp -r "$LOCAL_PROJECT_PATH/public/js/"* "$TEMP_DIR/public/js/" 2>/dev/null || true
    log_message "INFO" "JS files copied."
  fi

  # Copy assets directory (images, etc.)
  if [ -d "$LOCAL_PROJECT_PATH/public/assets" ]; then
    cp -r "$LOCAL_PROJECT_PATH/public/assets/"* "$TEMP_DIR/public/assets/" 2>/dev/null || true
    log_message "INFO" "Asset files copied."
  fi

  # Copy other files in public root (favicon, robots.txt, sitemap.xml, etc.)
  for file in "$LOCAL_PROJECT_PATH/public/"*.{ico,png,svg,txt,xml,php}; do
    if [ -f "$file" ]; then
      cp "$file" "$TEMP_DIR/public/" 2>/dev/null || true
    fi
  done
  log_message "INFO" "Root public files copied."

  # Copy built assets
  if [ -d "$LOCAL_PROJECT_PATH/public/build" ]; then
    cp -r "$LOCAL_PROJECT_PATH/public/build/"* "$TEMP_DIR/public/build/" 2>/dev/null || true
    log_message "INFO" "Build assets copied."
  fi

  # Copy documentation files
  if [ -d "$LOCAL_PROJECT_PATH/public/docs" ]; then
    mkdir -p "$TEMP_DIR/public/docs"
    cp -r "$LOCAL_PROJECT_PATH/public/docs/"* "$TEMP_DIR/public/docs/" 2>/dev/null || true
    log_message "INFO" "Documentation files copied."
  fi

  # Create .gitignore files for empty directories
  for dir in "storage/app/public" "storage/framework/cache" "storage/framework/sessions" "storage/framework/views" "storage/logs"; do
    touch "$TEMP_DIR/$dir/.gitignore"
  done

  log_message "INFO" "✅ Deployment files prepared successfully."
}

# Build frontend assets
build_frontend_assets

# Prepare deployment files
prepare_deployment_files

# ======== FILE OPERATIONS ========
# Backup critical files on the server
backup_critical_files() {
  log_message "INFO" "📑 Backing up existing index.php and .htaccess..."

  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
    if [ -f $REMOTE_PUBLIC_HTML/index.php ]; then
      cp $REMOTE_PUBLIC_HTML/index.php $REMOTE_PUBLIC_HTML/index.php.bak
    fi
    if [ -f $REMOTE_PUBLIC_HTML/.htaccess ]; then
      cp $REMOTE_PUBLIC_HTML/.htaccess $REMOTE_PUBLIC_HTML/.htaccess.bak
    fi
  "

  if [ $? -ne 0 ]; then
    log_message "WARN" "⚠️ Failed to backup files. Continuing anyway..."
  else
    log_message "INFO" "✅ Files backed up successfully."
  fi
}

# Upload project files to the server
upload_project_files() {
  log_message "INFO" "📤 Uploading essential project files to $REMOTE_PUBLIC_HTML..."

  # Define rsync options
  RSYNC_OPTS="-avz --progress --exclude='index.php' --exclude='.htaccess'"

  # Upload files
  sshpass -p "$SSHPASS" rsync $RSYNC_OPTS -e "ssh -p $REMOTE_PORT" "$TEMP_DIR/" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_PUBLIC_HTML/"

  if [ $? -ne 0 ]; then
    log_message "ERROR" "❌ Upload failed. Exiting."
    rollback
  else
    log_message "INFO" "✅ Files uploaded successfully."
  fi
}

# Create or update index.php file
create_index_php() {
  log_message "INFO" "✏️ Creating optimized index.php file..."

  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "cat > $REMOTE_PUBLIC_HTML/index.php" <<EOF
<?php

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

define('LARAVEL_START', microtime(true));

// Determine if the application is in maintenance mode...
if (file_exists(\$maintenance = __DIR__.'/storage/framework/maintenance.php')) {
    require \$maintenance;
}

// Register the Composer autoloader...
require __DIR__.'/vendor/autoload.php';

// Bootstrap Laravel and handle the request...
/** @var Application \$app */
\$app = require_once __DIR__.'/bootstrap/app.php';

\$app->handleRequest(Request::capture());
EOF

  if [ $? -ne 0 ]; then
    log_message "ERROR" "❌ Failed to create index.php. Exiting."
    rollback
  else
    log_message "INFO" "✅ index.php created successfully."
  fi
}

# Create or update .htaccess file
create_htaccess() {
  log_message "INFO" "✏️ Creating optimized .htaccess files..."

  # Create root .htaccess
  log_message "INFO" "✏️ Creating root .htaccess file..."
  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "cat > $REMOTE_PUBLIC_HTML/.htaccess" <<EOF
<IfModule mod_rewrite.c>
    RewriteEngine On

    # Special rule for sitemap.xml - serve directly if it exists
    RewriteCond %{REQUEST_URI} ^/sitemap\.xml$ [NC]
    RewriteCond %{DOCUMENT_ROOT}/sitemap.xml -f
    RewriteRule ^sitemap\.xml$ sitemap.xml [L]

    # Redirect non-www to www (except for localhost and IP addresses)
    # This rule must come before any other rules
    RewriteCond %{HTTP_HOST} ^endpointsync\.com$ [NC]
    RewriteCond %{REQUEST_URI} !^/sitemap\.xml$ [NC]
    RewriteRule ^(.*)$ http://www.endpointsync.com/\$1 [R=301,L]

    # Generic rule for any other domains without www
    RewriteCond %{HTTP_HOST} !^www\. [NC]
    RewriteCond %{HTTP_HOST} !^localhost [NC]
    RewriteCond %{HTTP_HOST} !^127\. [NC]
    RewriteCond %{HTTP_HOST} !^10\. [NC]
    RewriteCond %{HTTP_HOST} !^172\.(1[6-9]|2[0-9]|3[0-1])\. [NC]
    RewriteCond %{HTTP_HOST} !^192\.168\. [NC]
    RewriteCond %{HTTP_HOST} !\d+\.\d+\.\d+\.\d+ [NC]
    RewriteCond %{REQUEST_URI} !^/sitemap\.xml$ [NC]
    RewriteCond %{HTTPS}s ^on(s)|
    RewriteRule ^ http%1://www.%{HTTP_HOST}%{REQUEST_URI} [R=301,L]

    # Send all requests to the public folder
    RewriteCond %{REQUEST_URI} !^/public/
    RewriteRule ^(.*)$ public/\$1 [L]
</IfModule>
EOF

  # Create public/.htaccess
  log_message "INFO" "✏️ Creating public/.htaccess file..."
  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "cat > $REMOTE_PUBLIC_HTML/public/.htaccess" <<EOF
<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Special rule for sitemap.xml - serve directly if it exists
    RewriteCond %{REQUEST_URI} ^/sitemap\.xml$ [NC]
    RewriteCond %{DOCUMENT_ROOT}/sitemap.xml -f
    RewriteRule ^sitemap\.xml$ sitemap.xml [L]

    # Redirect non-www to www (except for localhost and IP addresses)
    # This rule must come before any other rules
    RewriteCond %{HTTP_HOST} ^endpointsync\.com$ [NC]
    RewriteCond %{REQUEST_URI} !^/sitemap\.xml$ [NC]
    RewriteRule ^(.*)$ http://www.endpointsync.com/\$1 [R=301,L]

    # Generic rule for any other domains without www
    RewriteCond %{HTTP_HOST} !^www\. [NC]
    RewriteCond %{HTTP_HOST} !^localhost [NC]
    RewriteCond %{HTTP_HOST} !^127\. [NC]
    RewriteCond %{HTTP_HOST} !^10\. [NC]
    RewriteCond %{HTTP_HOST} !^172\.(1[6-9]|2[0-9]|3[0-1])\. [NC]
    RewriteCond %{HTTP_HOST} !^192\.168\. [NC]
    RewriteCond %{HTTP_HOST} !\d+\.\d+\.\d+\.\d+ [NC]
    RewriteCond %{REQUEST_URI} !^/sitemap\.xml$ [NC]
    RewriteCond %{HTTPS}s ^on(s)|
    RewriteRule ^ http%1://www.%{HTTP_HOST}%{REQUEST_URI} [R=301,L]

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Handle X-XSRF-Token Header
    RewriteCond %{HTTP:x-xsrf-token} .
    RewriteRule .* - [E=HTTP_X_XSRF_TOKEN:%{HTTP:X-XSRF-Token}]

    # Ensure admin routes are properly handled by Laravel
    RewriteRule ^admin(/.*)?$ index.php [L]

    # Ensure dashboard route is properly handled by Laravel
    RewriteRule ^dashboard(/.*)?$ index.php [L]

    # Ensure settings routes are properly handled by Laravel
    RewriteRule ^settings(/.*)?$ index.php [L]

    # Redirect CSS, JS, and asset requests to the public folder
    RewriteCond %{REQUEST_URI} ^/css/(.*)$
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^css/(.*)$ public/css/\$1 [L]

    RewriteCond %{REQUEST_URI} ^/js/(.*)$
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^js/(.*)$ public/js/\$1 [L]

    # Handle assets directory (images, etc.)
    RewriteCond %{REQUEST_URI} ^/assets/(.*)$
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^assets/(.*)$ public/assets/\$1 [L]

    # Handle image files directly
    RewriteCond %{REQUEST_URI} \.(jpg|jpeg|png|gif|svg|webp)$
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(.*)$ public/\$1 [L]

    # Redirect Trailing Slashes If Not A Folder
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
EOF

  if [ $? -ne 0 ]; then
    log_message "ERROR" "❌ Failed to create .htaccess. Exiting."
    rollback
  else
    log_message "INFO" "✅ .htaccess created successfully."
  fi
}

# Backup critical files
backup_critical_files

# Upload project files
upload_project_files

# Create index.php
create_index_php

# Create .htaccess
create_htaccess

# ======== SERVER SETUP ========
# Set proper file permissions
set_file_permissions() {
  log_message "INFO" "🔐 Setting proper folder permissions..."

  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
    chmod -R 755 $REMOTE_PUBLIC_HTML &&
    chmod -R 775 $REMOTE_PUBLIC_HTML/storage $REMOTE_PUBLIC_HTML/bootstrap/cache
  "

  if [ $? -ne 0 ]; then
    log_message "WARN" "⚠️ Failed to set permissions. This may cause issues."
  else
    log_message "INFO" "✅ Permissions set successfully."
  fi
}

# Check and create .env file if needed
setup_env_file() {
  log_message "INFO" "🔍 Checking for .env file on server..."

  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
    if [ ! -f $REMOTE_PUBLIC_HTML/.env ]; then
      echo -e \"⚠️ No .env file found on server. Creating a basic one from .env.example...\"
      if [ -f $REMOTE_PUBLIC_HTML/.env.example ]; then
        cp $REMOTE_PUBLIC_HTML/.env.example $REMOTE_PUBLIC_HTML/.env
        echo \"APP_ENV=production\" >> $REMOTE_PUBLIC_HTML/.env
        echo \"APP_DEBUG=false\" >> $REMOTE_PUBLIC_HTML/.env
        echo \"✅ Basic .env file created from .env.example.\"
      else
        echo -e \"❌ No .env.example file found. You'll need to create an .env file manually.\"
      fi
    else
      echo -e \"✅ .env file exists on server.\"
    fi
  "
}

# Run composer update on server
run_composer_update() {
  log_message "INFO" "🔧 Running composer update on server..."

  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
    cd $REMOTE_PUBLIC_HTML &&
    $REMOTE_PHP $REMOTE_COMPOSER update --optimize-autoloader
  "

  if [ $? -ne 0 ]; then
    log_message "WARN" "⚠️ Composer update failed. Trying to install required packages individually..."

    # Try to install faker specifically
    sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
      cd $REMOTE_PUBLIC_HTML &&
      $REMOTE_PHP $REMOTE_COMPOSER require fakerphp/faker --dev
    "

    if [ $? -ne 0 ]; then
      log_message "WARN" "⚠️ Failed to install Faker. Database seeding might fail."
    else
      log_message "INFO" "✅ Faker installed successfully."
    fi
  else
    log_message "INFO" "✅ Composer update completed successfully."
  fi
}

# Create storage symlink if needed
setup_storage_symlink() {
  log_message "INFO" "🔍 Checking storage symlink..."

  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
    if [ ! -L $REMOTE_PUBLIC_HTML/public/storage ]; then
      echo -e \"⚠️ Storage symlink not found. Creating it now...\"
      cd $REMOTE_PUBLIC_HTML && $REMOTE_PHP artisan storage:link
      if [ \$? -eq 0 ]; then
        echo -e \"✅ Storage symlink created successfully.\"
      else
        echo -e \"❌ Failed to create storage symlink.\"
      fi
    else
      echo -e \"✅ Storage symlink exists.\"
    fi
  "
}

# Set file permissions
set_file_permissions

# Setup environment file
setup_env_file

# Run composer update
run_composer_update

# Setup storage symlink
setup_storage_symlink

# ======== DATABASE OPERATIONS ========
# Run database migrations
run_migrations() {
  log_message "INFO" "🔄 Running database migrations (preserving existing data)..."

  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
    cd $REMOTE_PUBLIC_HTML &&
    $REMOTE_PHP artisan migrate --force
  "

  if [ $? -ne 0 ]; then
    log_message "ERROR" "❌ Database migrations failed. Please check your database configuration."
    return 1
  else
    log_message "INFO" "✅ Database migrations completed successfully."
    return 0
  fi
}

# Reset database and run migrations
reset_database() {
  log_message "WARN" "⚠️ WARNING: This will RESET the database. All existing data will be PERMANENTLY DELETED!"
  log_message "WARN" "⚠️ Are you absolutely sure? Type 'RESET' to confirm:"
  read -r CONFIRM

  if [[ $CONFIRM == "RESET" ]]; then
    log_message "INFO" "🔄 Resetting database and running migrations..."

    sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
      cd $REMOTE_PUBLIC_HTML &&
      $REMOTE_PHP artisan migrate:fresh --force
    "

    if [ $? -ne 0 ]; then
      log_message "ERROR" "❌ Database reset failed. Check for errors."
      return 1
    else
      log_message "INFO" "✅ Database has been reset and migrations completed successfully."
      return 0
    fi
  else
    log_message "INFO" "Database reset cancelled. No changes were made."
    return 0
  fi
}

# Check and install Faker if needed
ensure_faker_installed() {
  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
    cd $REMOTE_PUBLIC_HTML &&
    if ! $REMOTE_PHP -r 'exit(class_exists(\"Faker\\\\Factory\") ? 0 : 1);'; then
      echo \"⚠️ Faker not found. Installing it now...\"
      $REMOTE_PHP $REMOTE_COMPOSER require fakerphp/faker --dev
      exit \$?
    else
      echo \"✅ Faker is already installed.\"
      exit 0
    fi
  "
  return $?
}

# Reset database and run migrations with seeders
reset_and_seed_database() {
  log_message "WARN" "⚠️ WARNING: This will RESET the database AND run seeders. All existing data will be PERMANENTLY DELETED!"
  log_message "WARN" "⚠️ Are you absolutely sure? Type 'RESET+SEED' to confirm:"
  read -r CONFIRM

  if [[ $CONFIRM == "RESET+SEED" ]]; then
    log_message "INFO" "🔄 Resetting database, running migrations and seeders..."

    # First check if Faker is installed
    ensure_faker_installed

    # Now run the migrations and seeders
    sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
      cd $REMOTE_PUBLIC_HTML &&
      $REMOTE_PHP artisan migrate:fresh --seed --force
    "

    if [ $? -ne 0 ]; then
      log_message "ERROR" "❌ Database reset and seeding failed. Trying migrations without seeders..."

      # Try running migrations without seeders as a fallback
      sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
        cd $REMOTE_PUBLIC_HTML &&
        $REMOTE_PHP artisan migrate:fresh --force
      "

      if [ $? -ne 0 ]; then
        log_message "ERROR" "❌ Database migrations also failed. Please check your database configuration."
        return 1
      else
        log_message "INFO" "✅ Database has been reset and migrations completed successfully (without seeders)."
        return 0
      fi
    else
      log_message "INFO" "✅ Database has been reset, migrations and seeders completed successfully."
      return 0
    fi
  else
    log_message "INFO" "Database reset and seeding cancelled. No changes were made."
    return 0
  fi
}

# Run seeders only
run_seeders() {
  log_message "INFO" "🌱 Running database seeders (preserving existing data)..."
  log_message "WARN" "⚠️ This will add seed data to your existing database. Continue? (y/n)"
  read -n 1 -r CONFIRM
  echo

  if [[ $CONFIRM =~ ^[Yy]$ ]]; then
    log_message "INFO" "🔄 Running database seeders..."

    # First check if Faker is installed
    ensure_faker_installed

    # Now run the seeders
    sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
      cd $REMOTE_PUBLIC_HTML &&
      $REMOTE_PHP artisan db:seed --force
    "

    if [ $? -ne 0 ]; then
      log_message "WARN" "❌ Database seeding failed. Trying JobStatusSeeder only..."

      # Try running just the JobStatusSeeder as a fallback
      sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
        cd $REMOTE_PUBLIC_HTML &&
        $REMOTE_PHP artisan db:seed --class=JobStatusSeeder --force
      "

      if [ $? -ne 0 ]; then
        log_message "ERROR" "❌ JobStatusSeeder also failed. Please check your database configuration."
        return 1
      else
        log_message "INFO" "✅ JobStatusSeeder completed successfully."
        return 0
      fi
    else
      log_message "INFO" "✅ Database seeders completed successfully."
      return 0
    fi
  else
    log_message "INFO" "Database seeding cancelled. No changes were made."
    return 0
  fi
}

# Database operations menu
database_operations() {
  log_message "INFO" "📊 Database Operations:"
  echo -e "  ${GREEN}1${NC} - Run migrations only (safe, preserves data)"
  echo -e "  ${YELLOW}2${NC} - Reset database and run migrations (WARNING: deletes all data)"
  echo -e "  ${RED}3${NC} - Reset database, run migrations AND seeders (WARNING: deletes all data)"
  echo -e "  ${GREEN}4${NC} - Run seeders only (keeps existing data)"
  echo -e "  ${GREEN}5${NC} - Skip database operations"
  echo -e "${YELLOW}Choose an option [1-5]:${NC}"
  read -n 1 -r DB_OPTION
  echo

  case $DB_OPTION in
    1) run_migrations ;;
    2) reset_database ;;
    3) reset_and_seed_database ;;
    4) run_seeders ;;
    5) log_message "INFO" "Skipping database operations..." ;;
    *) log_message "WARN" "Invalid option. Skipping database operations." ;;
  esac
}

# Run database operations
database_operations

# ======== CACHE MANAGEMENT ========
# Clear and rebuild Laravel caches
manage_caches() {
  log_message "INFO" "🧹 Clearing and rebuilding Laravel caches..."

  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
    cd $REMOTE_PUBLIC_HTML &&

    # First clear all caches
    echo 'Clearing caches...'
    $REMOTE_PHP artisan route:clear &&
    $REMOTE_PHP artisan cache:clear &&
    $REMOTE_PHP artisan config:clear &&
    $REMOTE_PHP artisan view:clear &&

    # Then rebuild caches for production
    echo 'Rebuilding caches...'
    $REMOTE_PHP artisan optimize:clear &&
    $REMOTE_PHP artisan config:cache &&
    $REMOTE_PHP artisan route:cache &&
    $REMOTE_PHP artisan view:cache
  "

  if [ $? -ne 0 ]; then
    log_message "WARN" "⚠️ Cache rebuilding failed. You may need to check for errors."
  else
    log_message "INFO" "✅ Laravel caches cleared and rebuilt successfully."
  fi
}

# ======== USER MANAGEMENT ========
# Check if is_admin column exists in users table and migrate role if needed
check_admin_column() {
  log_message "INFO" "Checking for is_admin column in users table..."

  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
    cd $REMOTE_PUBLIC_HTML &&
    $REMOTE_PHP artisan migrate:status

    # Verify the is_admin column exists
    if ! $REMOTE_PHP artisan tinker --execute=\"return Schema::hasColumn('users', 'is_admin') ? 'exists' : 'missing';\" | grep -q 'exists'; then
      echo 'is_admin column not found. Creating migration for is_admin column...'
      cat > database/migrations/\$(date +%Y_%m_%d_%H%M%S)_add_is_admin_to_users_table.php << 'EOL'
<?php

use Illuminate\\Database\\Migrations\\Migration;
use Illuminate\\Database\\Schema\\Blueprint;
use Illuminate\\Support\\Facades\\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint \$table) {
            if (!Schema::hasColumn('users', 'is_admin')) {
                \$table->boolean('is_admin')->default(false)->after('password');
            }
        });

        // If the role column exists, migrate any admin roles to is_admin=true
        if (Schema::hasColumn('users', 'role')) {
            DB::table('users')->where('role', 'admin')->update(['is_admin' => true]);
        }
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint \$table) {
            if (Schema::hasColumn('users', 'is_admin')) {
                \$table->dropColumn('is_admin');
            }
        });
    }
};
EOL

      # Run the migration
      echo 'Running migration...'
      $REMOTE_PHP artisan migrate --force

      if [ \$? -eq 0 ]; then
        echo 'is_admin column migration successful.'
        return 0
      else
        echo 'is_admin column migration failed.'
        return 1
      fi
    else
      echo 'is_admin column already exists.'

      # Check if we need to migrate any users from role to is_admin
      if $REMOTE_PHP artisan tinker --execute=\"return Schema::hasColumn('users', 'role') ? 'exists' : 'missing';\" | grep -q 'exists'; then
        echo 'Migrating any users with role=admin to is_admin=true...'
        $REMOTE_PHP artisan tinker --execute=\"DB::table('users')->where('role', 'admin')->update(['is_admin' => true]); return 'Migration complete';\"
      fi

      return 0
    fi
  "

  return $?
}

# Create or update admin user
create_admin_user() {
  log_message "INFO" "👤 Do you want to create an admin user? (y/n)"
  read -n 1 -r
  echo

  if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_message "INFO" "Please enter the admin user details:"
    read -p "Email: " ADMIN_EMAIL
    read -p "Name: " ADMIN_NAME
    read -s -p "Password: " ADMIN_PASSWORD
    echo

    # Validate inputs
    if [[ -z "$ADMIN_EMAIL" || -z "$ADMIN_NAME" || -z "$ADMIN_PASSWORD" ]]; then
      log_message "ERROR" "❌ All fields are required. Admin user creation cancelled."
      return 1
    fi

    # Check if email is valid
    if [[ ! "$ADMIN_EMAIL" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
      log_message "ERROR" "❌ Invalid email format. Admin user creation cancelled."
      return 1
    fi

    # Check if the is_admin column exists
    check_admin_column

    # Create the admin user
    log_message "INFO" "🔧 Creating/updating admin user..."

    sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
      cd $REMOTE_PUBLIC_HTML &&

      # Create a simple PHP script to create/update the user
      cat > create_admin.php << 'EOL'
<?php
require __DIR__ . '/vendor/autoload.php';
\$app = require_once __DIR__ . '/bootstrap/app.php';
\$app->make(Illuminate\\Contracts\\Console\\Kernel::class)->bootstrap();

use App\\Models\\User;
use Illuminate\\Support\\Facades\\Hash;

\$email = \$argv[1] ?? null;
\$name = \$argv[2] ?? null;
\$password = \$argv[3] ?? null;

if (!(\$email && \$name && \$password)) {
    echo \"Error: Missing required parameters\\n\";
    exit(1);
}

try {
    \$user = User::firstOrNew(['email' => \$email]);
    \$user->name = \$name;
    \$user->password = Hash::make(\$password);
    \$user->is_admin = true; // Set is_admin to true
    \$user->save();

    // Verify the user was saved with admin privileges
    \$savedUser = User::find(\$user->id);
    if (!\$savedUser || !\$savedUser->is_admin) {
        echo \"Error: Failed to set admin privileges for user {\$email}\\n\";
        exit(1);
    }

    echo \"Admin user {\$email} successfully \" . (\$user->wasRecentlyCreated ? 'created' : 'updated') . \" with admin privileges!\\n\";
    exit(0);
} catch (Exception \$e) {
    echo \"Error: {\$e->getMessage()}\\n\";
    exit(1);
}
EOL

      # Run the script
      $REMOTE_PHP create_admin.php '$ADMIN_EMAIL' '$ADMIN_NAME' '$ADMIN_PASSWORD'
      RESULT=\$?

      # Clean up
      rm create_admin.php

      exit \$RESULT
    "

    if [ $? -ne 0 ]; then
      log_message "ERROR" "❌ Admin user creation failed. Please check for errors."
      return 1
    else
      # Verify the admin user was created with admin privileges
      log_message "INFO" "Verifying admin privileges for user $ADMIN_EMAIL..."

      sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
        cd $REMOTE_PUBLIC_HTML &&

        # Create a verification script
        cat > verify_admin.php << 'EOL'
<?php
require __DIR__ . '/vendor/autoload.php';
\$app = require_once __DIR__ . '/bootstrap/app.php';
\$app->make(Illuminate\\Contracts\\Console\\Kernel::class)->bootstrap();

use App\\Models\\User;

\$email = \$argv[1] ?? null;

if (!\$email) {
    echo \"Error: Email parameter is required\\n\";
    exit(1);
}

try {
    \$user = User::where('email', \$email)->first();

    if (!\$user) {
        echo \"Error: User with email {\$email} not found\\n\";
        exit(1);
    }

    if (!\$user->is_admin) {
        echo \"Error: User {\$email} does not have admin privileges\\n\";
        exit(1);
    }

    echo \"Success: User {\$email} has admin privileges\\n\";
    exit(0);
} catch (Exception \$e) {
    echo \"Error: {\$e->getMessage()}\\n\";
    exit(1);
}
EOL

        # Run the verification script
        $REMOTE_PHP verify_admin.php '$ADMIN_EMAIL'
        VERIFY_RESULT=\$?

        # Clean up
        rm verify_admin.php

        exit \$VERIFY_RESULT
      "

      if [ $? -ne 0 ]; then
        log_message "ERROR" "❌ Admin privileges verification failed. The user may not have admin access."
        log_message "WARN" "You may need to manually set the is_admin field to true for this user."
        return 1
      else
        log_message "INFO" "✅ Admin user created/updated successfully with admin privileges."
        return 0
      fi
    fi
  else
    log_message "INFO" "Admin user creation skipped."
    return 0
  fi
}

# ======== ADMINLTE VERIFICATION ========
# Verify AdminLTE files are properly deployed
verify_adminlte_deployment() {
  log_message "INFO" "🎨 Verifying AdminLTE deployment..."

  sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
    cd $REMOTE_PUBLIC_HTML &&

    # Check if admin layout file exists
    if [ -f resources/views/components/layouts/admin.blade.php ]; then
      echo '✅ Admin layout file found'
    else
      echo '❌ Admin layout file missing'
      exit 1
    fi

    # Check if AdminLTE custom CSS exists
    if [ -f public/css/admin-lte-custom.css ]; then
      echo '✅ AdminLTE custom CSS found'
    else
      echo '❌ AdminLTE custom CSS missing'
      exit 1
    fi

    # Check if admin views directory exists
    if [ -d resources/views/admin ]; then
      echo '✅ Admin views directory found'

      # Count admin view files
      ADMIN_VIEW_COUNT=\$(find resources/views/admin -name '*.blade.php' | wc -l)
      echo \"✅ Found \$ADMIN_VIEW_COUNT admin view files\"
    else
      echo '❌ Admin views directory missing'
      exit 1
    fi

    # Verify key admin pages exist
    REQUIRED_ADMIN_VIEWS=(
      'resources/views/admin/dashboard.blade.php'
      'resources/views/admin/consultations/index.blade.php'
      'resources/views/admin/users/index.blade.php'
      'resources/views/admin/leads/index.blade.php'
      'resources/views/admin/landing-pages/index.blade.php'
      'resources/views/admin/consultation-statuses/index.blade.php'
    )

    MISSING_VIEWS=0
    for view in \"\${REQUIRED_ADMIN_VIEWS[@]}\"; do
      if [ -f \"\$view\" ]; then
        echo \"✅ \$view exists\"
      else
        echo \"❌ \$view missing\"
        MISSING_VIEWS=\$((MISSING_VIEWS + 1))
      fi
    done

    if [ \$MISSING_VIEWS -eq 0 ]; then
      echo '✅ All required admin views are present'
      exit 0
    else
      echo \"❌ \$MISSING_VIEWS admin views are missing\"
      exit 1
    fi
  "

  if [ $? -ne 0 ]; then
    log_message "WARN" "⚠️ AdminLTE deployment verification failed. Some files may be missing."
    return 1
  else
    log_message "INFO" "✅ AdminLTE deployment verified successfully."
    return 0
  fi
}

# Manage caches
manage_caches

# Verify AdminLTE deployment
verify_adminlte_deployment

# Create admin user
create_admin_user

# ======== COMPLETION ========
# Display completion message and next steps
show_completion_message() {
  # Get current timestamp for deployment record
  DEPLOY_TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

  # Print colorful header
  echo -e "\n${GREEN}════════════════════════════════════════════════════════════════${NC}"
  echo -e "${GREEN}✅ DEPLOYMENT COMPLETED SUCCESSFULLY AT ${DEPLOY_TIMESTAMP}${NC}"
  echo -e "${GREEN}════════════════════════════════════════════════════════════════${NC}\n"

  # Print next steps
  echo -e "${YELLOW}📋 NEXT STEPS:${NC}"
  echo -e "  ${BLUE}1.${NC} Verify your site is working correctly at:"
  echo -e "     ${GREEN}https://mediumslateblue-camel-310455.hostingersite.com${NC}"
  echo -e "  ${BLUE}2.${NC} Test the admin interface with AdminLTE v3 styling at:"
  echo -e "     ${GREEN}https://mediumslateblue-camel-310455.hostingersite.com/admin${NC}"
  echo -e "  ${BLUE}3.${NC} Check the server logs if you encounter any issues"
  echo -e "  ${BLUE}4.${NC} For production deployment, update the script with the production domain path\n"

  # Print script usage
  echo -e "${YELLOW}📝 SCRIPT USAGE:${NC}"
  echo -e "  ${BLUE}./deploy_test.sh${NC}                  # Deploy using stored password (if available)"
  echo -e "  ${BLUE}./deploy_test.sh --reset-password${NC} # Reset stored password and prompt for a new one\n"

  # Print database options
  echo -e "${YELLOW}🗄️ DATABASE OPTIONS:${NC}"
  echo -e "  ${BLUE}1${NC} - Run migrations only (safe, preserves data)"
  echo -e "  ${YELLOW}2${NC} - Reset database and run migrations (WARNING: deletes all data)"
  echo -e "  ${RED}3${NC} - Reset database, run migrations AND seeders (WARNING: deletes all data)"
  echo -e "  ${BLUE}4${NC} - Run seeders only (keeps existing data)"
  echo -e "  ${BLUE}5${NC} - Skip database operations\n"

  # Print AdminLTE features
  echo -e "${YELLOW}🎨 ADMINLTE v3 FEATURES DEPLOYED:${NC}"
  echo -e "  ${BLUE}✅${NC} Modern admin dashboard with professional styling"
  echo -e "  ${BLUE}✅${NC} Consistent layout across all admin pages"
  echo -e "  ${BLUE}✅${NC} Statistics boxes with key metrics on each page"
  echo -e "  ${BLUE}✅${NC} Responsive design for mobile and desktop"
  echo -e "  ${BLUE}✅${NC} EndpointSync branding with blue/gray/orange theme"
  echo -e "  ${BLUE}✅${NC} Enhanced navigation with collapsible sidebar"
  echo -e "  ${BLUE}✅${NC} Professional tables, cards, and form styling\n"

  # Print deployment record
  echo -e "${YELLOW}📊 DEPLOYMENT RECORD:${NC}"
  echo -e "  ${BLUE}Date:${NC} $DEPLOY_TIMESTAMP"
  echo -e "  ${BLUE}Server:${NC} $REMOTE_USER@$REMOTE_HOST:$REMOTE_PORT"
  echo -e "  ${BLUE}Path:${NC} $REMOTE_PUBLIC_HTML"
  echo -e "  ${BLUE}PHP Version:${NC} $(sshpass -p "$SSHPASS" ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "$REMOTE_PHP -r 'echo PHP_VERSION;'" 2>/dev/null || echo "Unknown")"
  echo -e "  ${BLUE}AdminLTE:${NC} v3.2.0 (via CDN)\n"
}

# Show completion message
show_completion_message