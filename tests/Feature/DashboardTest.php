<?php

use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('guests are redirected to the login page when accessing admin dashboard', function () {
    $response = $this->get('/admin');
    $response->assertRedirect('/login');
});

test('admin users can visit the admin dashboard', function () {
    $user = User::factory()->create(['is_admin' => true]);
    $this->actingAs($user);

    $response = $this->get('/admin');
    $response->assertStatus(200);
});

test('non-admin users are redirected when accessing admin dashboard', function () {
    $user = User::factory()->create(['is_admin' => false]);
    $this->actingAs($user);

    $response = $this->get('/admin');
    $response->assertRedirect('/');
});