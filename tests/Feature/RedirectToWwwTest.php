<?php

namespace Tests\Feature;

use App\Http\Middleware\RedirectToWww;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Tests\TestCase;

class RedirectToWwwTest extends TestCase
{
    /**
     * Test that non-www URLs are redirected to www.
     */
    public function test_non_www_urls_are_redirected_to_www(): void
    {
        // Create a mock request with a non-www domain
        $request = Request::create('http://endpointsync.com');

        // Create a mock response
        $response = new Response();

        // Create the middleware
        $middleware = new RedirectToWww();

        // Force the app environment to production to test the redirection
        app()->detectEnvironment(function() {
            return 'production';
        });

        // Process the request through the middleware
        $result = $middleware->handle($request, function($req) use ($response) {
            return $response;
        });

        // Assert that we get a redirect response
        $this->assertEquals(301, $result->getStatusCode());
        $this->assertEquals('http://www.endpointsync.com/', $result->headers->get('Location'));
    }

    /**
     * Test that www URLs are not redirected.
     */
    public function test_www_urls_are_not_redirected(): void
    {
        // Create a mock request with a www domain
        $request = Request::create('http://www.endpointsync.com');

        // Create a mock response
        $response = new Response();

        // Create the middleware
        $middleware = new RedirectToWww();

        // Force the app environment to production to test the redirection
        app()->detectEnvironment(function() {
            return 'production';
        });

        // Process the request through the middleware
        $result = $middleware->handle($request, function($req) use ($response) {
            return $response;
        });

        // Assert that we don't get a redirect
        $this->assertSame($response, $result);
    }

    /**
     * Test that localhost is not redirected.
     */
    public function test_localhost_is_not_redirected(): void
    {
        // Create a mock request with localhost
        $request = Request::create('http://localhost');

        // Create a mock response
        $response = new Response();

        // Create the middleware
        $middleware = new RedirectToWww();

        // Force the app environment to production to test the redirection
        app()->detectEnvironment(function() {
            return 'production';
        });

        // Process the request through the middleware
        $result = $middleware->handle($request, function($req) use ($response) {
            return $response;
        });

        // Assert that we don't get a redirect
        $this->assertSame($response, $result);
    }

    /**
     * Test that IP addresses are not redirected.
     */
    public function test_ip_addresses_are_not_redirected(): void
    {
        // Create a mock request with an IP address
        $request = Request::create('http://127.0.0.1');

        // Create a mock response
        $response = new Response();

        // Create the middleware
        $middleware = new RedirectToWww();

        // Force the app environment to production to test the redirection
        app()->detectEnvironment(function() {
            return 'production';
        });

        // Process the request through the middleware
        $result = $middleware->handle($request, function($req) use ($response) {
            return $response;
        });

        // Assert that we don't get a redirect
        $this->assertSame($response, $result);
    }
}
