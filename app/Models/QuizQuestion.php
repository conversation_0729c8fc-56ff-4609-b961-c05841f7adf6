<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class QuizQuestion extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'quiz_id',
        'category_id',
        'question',
        'type',
        'options',
        'points',
        'sort_order',
        'is_required',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'options' => 'array',
        'points' => 'integer',
        'sort_order' => 'integer',
        'is_required' => 'boolean',
    ];

    /**
     * Get the quiz that owns this question.
     */
    public function quiz(): BelongsTo
    {
        return $this->belongsTo(Quiz::class);
    }

    /**
     * Get the category that owns this question.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(QuizCategory::class, 'category_id');
    }

    /**
     * Get the question types available.
     */
    public static function getTypes(): array
    {
        return [
            'multiple_choice' => 'Multiple Choice',
            'single_choice' => 'Single Choice',
            'text' => 'Text Input',
            'scale' => 'Scale (1-10)',
        ];
    }

    /**
     * Check if this question has options.
     */
    public function hasOptions(): bool
    {
        return in_array($this->type, ['multiple_choice', 'single_choice']);
    }

    /**
     * Get formatted options for display.
     */
    public function getFormattedOptions(): array
    {
        if (!$this->hasOptions() || !$this->options) {
            return [];
        }

        $formatted = [];
        foreach ($this->options as $key => $option) {
            if (is_array($option)) {
                $formatted[$key] = $option;
            } else {
                // Legacy format - just text
                $formatted[$key] = [
                    'text' => $option,
                    'points' => 1
                ];
            }
        }

        return $formatted;
    }

    /**
     * Validate a response for this question.
     */
    public function validateResponse($response): array
    {
        $errors = [];

        // Check if required question is answered
        if ($this->is_required && ($response === null || $response === '' || (is_array($response) && empty($response)))) {
            $errors[] = "This question is required.";
            return ['valid' => false, 'errors' => $errors];
        }

        // Skip validation if not required and empty
        if (!$this->is_required && ($response === null || $response === '')) {
            return ['valid' => true, 'errors' => []];
        }

        // Type-specific validation
        switch ($this->type) {
            case 'multiple_choice':
                if (!is_array($response)) {
                    $errors[] = "This question expects multiple selections.";
                    break;
                }

                $options = $this->getFormattedOptions();
                $validOptions = array_keys($options);

                foreach ($response as $selectedOption) {
                    if (!in_array($selectedOption, $validOptions)) {
                        $errors[] = "Invalid option selected.";
                        break;
                    }
                }
                break;

            case 'single_choice':
                if (is_array($response)) {
                    $errors[] = "This question expects only one selection.";
                    break;
                }

                $options = $this->getFormattedOptions();
                $validOptions = array_keys($options);

                if (!in_array($response, $validOptions)) {
                    $errors[] = "Invalid option selected.";
                }
                break;

            case 'text':
                if (!is_string($response)) {
                    $errors[] = "This question expects a text response.";
                    break;
                }

                $trimmed = trim($response);
                if (strlen($trimmed) < 2) {
                    $errors[] = "Response is too short (minimum 2 characters).";
                } elseif (strlen($response) > 1000) {
                    $errors[] = "Response is too long (maximum 1000 characters).";
                }
                break;

            case 'scale':
                if (!is_numeric($response)) {
                    $errors[] = "This question expects a numeric value.";
                    break;
                }

                $value = (int) $response;
                if ($value < 1 || $value > 10) {
                    $errors[] = "Value must be between 1 and 10.";
                }
                break;
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Get the maximum possible points for this question.
     */
    public function getMaxPoints(): int
    {
        switch ($this->type) {
            case 'multiple_choice':
                $options = $this->getFormattedOptions();
                return array_sum(array_column($options, 'points'));

            case 'single_choice':
                $options = $this->getFormattedOptions();
                return max(array_column($options, 'points'));

            case 'scale':
                return 10; // Scale questions max value

            case 'text':
            default:
                return $this->points;
        }
    }

    /**
     * Check if this question is properly configured.
     */
    public function isValid(): bool
    {
        if (empty(trim($this->question))) {
            return false;
        }

        if ($this->hasOptions() && empty($this->options)) {
            return false;
        }

        if ($this->hasOptions()) {
            $options = $this->getFormattedOptions();
            if (count($options) < 2) {
                return false;
            }
        }

        return true;
    }
}
