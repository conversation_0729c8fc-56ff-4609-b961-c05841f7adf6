<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Consultation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'website_url',
        'business_description',
        'pain_point',
        'current_automation',
        'additional_notes',
        'status_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status_id' => 'integer',
    ];

    /**
     * Get the status of this consultation.
     */
    public function status(): BelongsTo
    {
        return $this->belongsTo(ConsultationStatus::class, 'status_id');
    }

    /**
     * Scope a query to only include consultations with a specific status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int|string  $status  Status ID or name
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithStatus($query, $status)
    {
        if (is_numeric($status)) {
            return $query->where('status_id', $status);
        }

        return $query->whereHas('status', function ($query) use ($status) {
            $query->where('name', $status);
        });
    }

    /**
     * Check if the consultation has a specific status.
     *
     * @param  int|string  $status  Status ID or name
     * @return bool
     */
    public function hasStatus($status): bool
    {
        if (is_numeric($status)) {
            return $this->status_id == $status;
        }

        return $this->status && $this->status->name === $status;
    }

    /**
     * Update the status of this consultation.
     *
     * @param  int|string  $status  Status ID or name
     * @return bool
     */
    public function updateStatus($status): bool
    {
        if (is_numeric($status)) {
            return $this->update(['status_id' => $status]);
        }

        $statusModel = ConsultationStatus::where('name', $status)->first();

        if ($statusModel) {
            return $this->update(['status_id' => $statusModel->id]);
        }

        return false;
    }
}
