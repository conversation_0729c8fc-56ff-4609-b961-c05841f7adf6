<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'value',
        'group',
        'type',
        'label',
        'description',
    ];

    /**
     * Get a setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        // Try to get from cache first
        if (Cache::has('setting_' . $key)) {
            return Cache::get('setting_' . $key);
        }

        // If not in cache, get from database
        $setting = self::where('key', $key)->first();
        
        // If setting exists, cache it and return the value
        if ($setting) {
            Cache::put('setting_' . $key, $setting->value, now()->addDay());
            return $setting->value;
        }
        
        // Return default if setting doesn't exist
        return $default;
    }

    /**
     * Set a setting value
     *
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public static function set(string $key, $value): bool
    {
        $setting = self::where('key', $key)->first();
        
        if ($setting) {
            $updated = $setting->update(['value' => $value]);
            
            if ($updated) {
                Cache::put('setting_' . $key, $value, now()->addDay());
            }
            
            return $updated;
        }
        
        return false;
    }

    /**
     * Get all settings by group
     *
     * @param string $group
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByGroup(string $group)
    {
        return self::where('group', $group)->get();
    }

    /**
     * Clear the cache for a specific setting
     *
     * @param string $key
     * @return void
     */
    public static function clearCache(string $key): void
    {
        Cache::forget('setting_' . $key);
    }

    /**
     * Clear all settings cache
     *
     * @return void
     */
    public static function clearAllCache(): void
    {
        $settings = self::all();
        
        foreach ($settings as $setting) {
            Cache::forget('setting_' . $setting->key);
        }
    }
}
