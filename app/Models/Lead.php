<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Lead extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'landing_page_id',
        'name',
        'email',
        'company_name',
        'phone_number',
        'availability',
        'quiz_score',
        'quiz_responses',
        'pdf_sent',
        'pdf_sent_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quiz_responses' => 'array',
        'pdf_sent' => 'boolean',
        'pdf_sent_at' => 'datetime',
        'quiz_score' => 'integer',
    ];

    /**
     * Get the landing page that owns this lead.
     */
    public function landingPage(): BelongsTo
    {
        return $this->belongsTo(LandingPage::class);
    }

    /**
     * Get the lead's initials for display.
     */
    public function getInitialsAttribute(): string
    {
        $names = explode(' ', $this->name);
        $initials = '';
        
        foreach ($names as $name) {
            $initials .= strtoupper(substr($name, 0, 1));
        }
        
        return $initials;
    }

    /**
     * Get the quiz score as a percentage.
     */
    public function getQuizScorePercentageAttribute(): ?float
    {
        if ($this->quiz_score === null) {
            return null;
        }

        // Assuming max score is 100, adjust as needed
        return round(($this->quiz_score / 100) * 100, 1);
    }
}
