<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ConsultationStatus extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'color',
        'description',
        'is_default',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_default' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the consultations with this status.
     */
    public function consultations(): HasMany
    {
        return $this->hasMany(Consultation::class, 'status_id');
    }

    /**
     * Get the default status.
     *
     * @return \App\Models\ConsultationStatus|null
     */
    public static function getDefault()
    {
        return static::where('is_default', true)->first();
    }

    /**
     * Set this status as the default.
     *
     * @return bool
     */
    public function setAsDefault(): bool
    {
        // First, unset any existing default
        static::where('is_default', true)->update(['is_default' => false]);

        // Then set this one as default
        return $this->update(['is_default' => true]);
    }

    /**
     * Ensure there is at least one default status.
     * If no default status exists, create a "Pending" status as default.
     *
     * @return \App\Models\ConsultationStatus
     */
    public static function ensureDefaultStatusExists()
    {
        // Check if any default status exists
        $defaultStatus = static::where('is_default', true)->first();

        if (!$defaultStatus) {
            // Check if any status exists at all
            $anyStatus = static::first();

            if ($anyStatus) {
                // Set the first status as default
                $anyStatus->setAsDefault();
                return $anyStatus;
            } else {
                // Create a new "Pending" status as default
                $defaultStatus = static::create([
                    'name' => 'Pending',
                    'color' => '#6c757d', // Gray color
                    'description' => 'Default status for new consultation requests',
                    'is_default' => true,
                    'sort_order' => 0,
                ]);

                return $defaultStatus;
            }
        }

        return $defaultStatus;
    }
}
