<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Validator;

class CreateApiToken extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'api:token:create {email} {name?} {--abilities=*}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new API token for a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $tokenName = $this->argument('name') ?? 'API Token';
        $abilities = $this->option('abilities') ?: ['*'];

        // Validate email
        $validator = Validator::make(['email' => $email], [
            'email' => 'required|email|exists:users,email',
        ]);

        if ($validator->fails()) {
            $this->error('Invalid email or user not found.');
            return 1;
        }

        // Find the user
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error('User not found.');
            return 1;
        }

        // Create token
        $token = $user->createToken($tokenName, $abilities);

        $this->info('API token created successfully!');
        $this->newLine();
        $this->info('Token: ' . $token->plainTextToken);
        $this->newLine();
        $this->warn('Please save this token securely as it will not be shown again.');

        return 0;
    }
}
