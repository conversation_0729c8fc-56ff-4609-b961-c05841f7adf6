<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class CreateUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:create 
                            {--name= : The name of the user}
                            {--email= : The email of the user}
                            {--password= : The password for the user}
                            {--admin : Make the user an admin}
                            {--verified : Mark the email as verified}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get or prompt for user details
        $name = $this->option('name') ?: $this->ask('Enter the name for the user');
        $email = $this->option('email') ?: $this->ask('Enter the email for the user');
        $password = $this->option('password') ?: $this->secret('Enter the password for the user');
        $isAdmin = $this->option('admin') ?: $this->confirm('Should this user be an admin?', false);
        $isVerified = $this->option('verified') ?: $this->confirm('Should this user\'s email be marked as verified?', true);

        // Validate input
        $validator = Validator::make([
            'name' => $name,
            'email' => $email,
            'password' => $password,
        ], [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8'],
        ]);

        if ($validator->fails()) {
            foreach ($validator->errors()->all() as $error) {
                $this->error($error);
            }
            return 1;
        }

        // Create the user
        $user = new User();
        $user->name = $name;
        $user->email = $email;
        $user->password = Hash::make($password);
        $user->is_admin = $isAdmin;
        
        if ($isVerified) {
            $user->email_verified_at = now();
        }
        
        $user->save();

        // Display success message
        $this->info("User {$user->name} created successfully!");
        $this->line("Email: {$user->email}");
        $this->line("Admin: " . ($user->is_admin ? 'Yes' : 'No'));
        $this->line("Email Verified: " . ($user->email_verified_at ? 'Yes' : 'No'));
        
        return 0;
    }
}
