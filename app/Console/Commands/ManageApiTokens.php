<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Validator;

class ManageApiTokens extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'api:token {action} {email?} {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage API tokens (list, revoke)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');
        
        switch ($action) {
            case 'list':
                return $this->listTokens();
            case 'revoke':
                return $this->revokeToken();
            default:
                $this->error("Unknown action: {$action}. Available actions: list, revoke");
                return 1;
        }
    }
    
    /**
     * List all tokens for a user or all users.
     */
    protected function listTokens()
    {
        $email = $this->argument('email');
        
        if ($email) {
            // Validate email
            $validator = Validator::make(['email' => $email], [
                'email' => 'required|email|exists:users,email',
            ]);

            if ($validator->fails()) {
                $this->error('Invalid email or user not found.');
                return 1;
            }
            
            $user = User::where('email', $email)->first();
            $this->displayUserTokens($user);
        } else {
            // List tokens for all users
            $users = User::has('tokens')->get();
            
            if ($users->isEmpty()) {
                $this->info('No API tokens found for any users.');
                return 0;
            }
            
            foreach ($users as $user) {
                $this->displayUserTokens($user);
                $this->newLine();
            }
        }
        
        return 0;
    }
    
    /**
     * Display tokens for a specific user.
     */
    protected function displayUserTokens(User $user)
    {
        $this->info("Tokens for {$user->name} ({$user->email}):");
        
        if ($user->tokens->isEmpty()) {
            $this->warn('  No tokens found for this user.');
            return;
        }
        
        $headers = ['ID', 'Name', 'Created At', 'Last Used At'];
        $rows = [];
        
        foreach ($user->tokens as $token) {
            $rows[] = [
                $token->id,
                $token->name,
                $token->created_at->format('Y-m-d H:i:s'),
                $token->last_used_at ? $token->last_used_at->format('Y-m-d H:i:s') : 'Never',
            ];
        }
        
        $this->table($headers, $rows);
    }
    
    /**
     * Revoke a specific token.
     */
    protected function revokeToken()
    {
        $email = $this->argument('email');
        $tokenId = $this->argument('id');
        
        if (!$email || !$tokenId) {
            $this->error('Both email and token ID are required for revocation.');
            return 1;
        }
        
        // Validate email
        $validator = Validator::make(['email' => $email], [
            'email' => 'required|email|exists:users,email',
        ]);

        if ($validator->fails()) {
            $this->error('Invalid email or user not found.');
            return 1;
        }
        
        $user = User::where('email', $email)->first();
        $token = $user->tokens()->find($tokenId);
        
        if (!$token) {
            $this->error("Token with ID {$tokenId} not found for user {$email}.");
            return 1;
        }
        
        $token->delete();
        $this->info("Token {$tokenId} has been revoked successfully.");
        
        return 0;
    }
}
