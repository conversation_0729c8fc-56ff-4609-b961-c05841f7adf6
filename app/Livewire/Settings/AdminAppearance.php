<?php

namespace App\Livewire\Settings;

use Livewire\Component;

class AdminAppearance extends Component
{
    public string $theme = 'light';
    
    public function mount(): void
    {
        // Get the user's current theme preference (could be stored in user settings)
        $this->theme = session('theme', 'light');
    }
    
    public function updateTheme(): void
    {
        // Save the theme preference (could be saved to user settings in database)
        session(['theme' => $this->theme]);
        
        session()->flash('success', 'Appearance settings updated successfully.');
    }
    
    /**
     * Render the component.
     */
    public function render()
    {
        return view('livewire.settings.admin-appearance')
            ->layout('layouts.settings', ['title' => 'Appearance Settings']);
    }
}
