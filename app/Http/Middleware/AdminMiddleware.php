<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            // Log the authentication failure
            \Log::warning('Admin access attempted without authentication', [
                'ip' => $request->ip(),
                'path' => $request->path(),
                'user_agent' => $request->userAgent(),
                'session_id' => session()->getId(),
                'has_session' => session()->isStarted(),
            ]);

            return redirect()->route('login')->with('error', 'Please log in to access the admin area.');
        }

        // Check if user is an admin
        if (!Auth::user()->is_admin) {
            // Log the authorization failure
            \Log::warning('Non-admin user attempted to access admin area', [
                'user_id' => Auth::id(),
                'user_email' => Auth::user()->email,
                'path' => $request->path(),
            ]);

            return redirect()->route('home')->with('error', 'You do not have permission to access this area.');
        }

        return $next($request);
    }
}
