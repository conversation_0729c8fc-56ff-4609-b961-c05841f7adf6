<?php

namespace App\Http\Middleware;

use App\Models\Setting;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class IpRestrictionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get allowed IPs from settings (falls back to environment variable)
        $allowedIpsString = Setting::get('allowed_api_ips', env('ALLOWED_API_IPS', ''));
        $allowedIps = explode(',', $allowedIpsString);

        // If no IPs are configured, allow all (for development)
        if (empty($allowedIps) && app()->environment('local', 'development', 'testing')) {
            return $next($request);
        }

        // Check if the client IP is in the allowed list
        $clientIp = $request->ip();
        if (!in_array($clientIp, $allowedIps)) {
            \Log::warning('API access attempted from unauthorized IP', [
                'ip' => $clientIp,
                'path' => $request->path(),
                'user_agent' => $request->userAgent(),
            ]);

            return response()->json([
                'error' => 'Access denied. Your IP is not authorized to access this API.',
            ], 403);
        }

        return $next($request);
    }
}
