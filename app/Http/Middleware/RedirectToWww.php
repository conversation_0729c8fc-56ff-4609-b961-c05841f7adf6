<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RedirectToWww
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip redirection for local development or testing
        if (app()->environment('local', 'development', 'testing')) {
            return $next($request);
        }

        // Skip redirection for sitemap.xml
        if ($request->path() === 'sitemap.xml') {
            \Log::info('Skipping www redirect for sitemap.xml');
            return $next($request);
        }

        // Get the current host
        $host = $request->getHost();

        // Log the host for debugging
        \Log::info('RedirectToWww middleware processing host: ' . $host);

        // Skip redirection for localhost and IP addresses
        if (str_starts_with($host, 'localhost') || filter_var($host, FILTER_VALIDATE_IP)) {
            \Log::info('Skipping www redirect for localhost or IP address');
            return $next($request);
        }

        // Check if the host starts with 'www.'
        if (!str_starts_with($host, 'www.')) {
            // Get the full URL
            $url = $request->getScheme() . '://www.' . $host . $request->getRequestUri();

            \Log::info('Redirecting to www version: ' . $url);

            // Redirect to the www version with a 301 (permanent) redirect
            return redirect()->to($url, 301);
        }

        \Log::info('Host already has www prefix, no redirect needed');
        return $next($request);
    }
}
