<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Consultation;
use App\Models\ConsultationStatus;
use Illuminate\Http\Request;

class ConsultationAdminController extends Controller
{
    // Middleware is now applied in the routes file

    /**
     * Display a listing of the consultations.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $query = Consultation::with('status')->latest();

        // Apply status filter if provided
        if ($request->has('status') && $request->status !== '') {
            $query->withStatus($request->status);
        }

        // Apply search filter if provided
        if ($request->has('search') && $request->search !== '') {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Apply date filter if provided
        if ($request->has('date') && $request->date !== '') {
            if ($request->date === 'today') {
                $query->whereDate('created_at', today());
            } elseif ($request->date === 'week') {
                $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
            } elseif ($request->date === 'month') {
                $query->whereMonth('created_at', now()->month)
                      ->whereYear('created_at', now()->year);
            }
        }

        $consultations = $query->paginate(10)->withQueryString();
        $statuses = ConsultationStatus::orderBy('sort_order')->get();

        return view('admin.consultations.index', compact('consultations', 'statuses'));
    }

    /**
     * Show the form for creating a new consultation.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $statuses = ConsultationStatus::orderBy('sort_order')->get();
        $defaultStatus = ConsultationStatus::getDefault();

        return view('admin.consultations.create', compact('statuses', 'defaultStatus'));
    }

    /**
     * Store a newly created consultation in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'website_url' => 'nullable|url|max:255',
            'business_description' => 'required|string',
            'pain_point' => 'required|string',
            'current_automation' => 'required|string',
            'additional_notes' => 'nullable|string',
            'status_id' => 'required|exists:consultation_statuses,id',
        ]);

        Consultation::create($validated);

        return redirect()->route('admin.consultations.index')
            ->with('success', 'Consultation created successfully.');
    }

    /**
     * Display the specified consultation.
     *
     * @param  \App\Models\Consultation  $consultation
     * @return \Illuminate\View\View
     */
    public function show(Consultation $consultation)
    {
        $consultation->load('status');
        $statuses = ConsultationStatus::orderBy('sort_order')->get();

        return view('admin.consultations.show', compact('consultation', 'statuses'));
    }

    /**
     * Show the form for editing the specified consultation.
     *
     * @param  \App\Models\Consultation  $consultation
     * @return \Illuminate\View\View
     */
    public function edit(Consultation $consultation)
    {
        $statuses = ConsultationStatus::orderBy('sort_order')->get();

        return view('admin.consultations.edit', compact('consultation', 'statuses'));
    }

    /**
     * Update the specified consultation in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Consultation  $consultation
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Consultation $consultation)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'website_url' => 'nullable|url|max:255',
            'business_description' => 'required|string',
            'pain_point' => 'required|string',
            'current_automation' => 'required|string',
            'additional_notes' => 'nullable|string',
            'status_id' => 'required|exists:consultation_statuses,id',
        ]);

        $consultation->update($validated);

        return redirect()->route('admin.consultations.index')
            ->with('success', 'Consultation updated successfully.');
    }

    /**
     * Remove the specified consultation from storage.
     *
     * @param  \App\Models\Consultation  $consultation
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Consultation $consultation)
    {
        $consultation->delete();

        return redirect()->route('admin.consultations.index')
            ->with('success', 'Consultation deleted successfully.');
    }

    /**
     * Update the status of a consultation.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Consultation  $consultation
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateStatus(Request $request, Consultation $consultation)
    {
        $validated = $request->validate([
            'status_id' => 'required|exists:consultation_statuses,id',
        ]);

        $consultation->update(['status_id' => $validated['status_id']]);

        $statusName = ConsultationStatus::find($validated['status_id'])->name;

        return redirect()->back()
            ->with('success', "Consultation status updated to '{$statusName}'.");
    }
}
