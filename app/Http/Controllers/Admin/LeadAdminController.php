<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Lead;
use App\Models\LandingPage;
use Illuminate\Http\Request;

class LeadAdminController extends Controller
{
    /**
     * Display a listing of leads.
     */
    public function index(Request $request)
    {
        $query = Lead::with('landingPage');

        // Filter by landing page
        if ($request->filled('landing_page_id')) {
            $query->where('landing_page_id', $request->landing_page_id);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by name or email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('company_name', 'like', "%{$search}%");
            });
        }

        $leads = $query->orderBy('created_at', 'desc')->paginate(20);
        $landingPages = LandingPage::orderBy('title')->get();

        return view('admin.leads.index', compact('leads', 'landingPages'));
    }

    /**
     * Display the specified lead.
     */
    public function show(Lead $lead)
    {
        $lead->load('landingPage');
        
        return view('admin.leads.show', compact('lead'));
    }

    /**
     * Show the form for editing the lead.
     */
    public function edit(Lead $lead)
    {
        $landingPages = LandingPage::orderBy('title')->get();
        
        return view('admin.leads.edit', compact('lead', 'landingPages'));
    }

    /**
     * Update the specified lead.
     */
    public function update(Request $request, Lead $lead)
    {
        $validated = $request->validate([
            'landing_page_id' => 'required|exists:landing_pages,id',
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'company_name' => 'required|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'availability' => 'nullable|string|max:1000',
            'quiz_score' => 'nullable|integer|min:0',
            'pdf_sent' => 'boolean',
        ]);

        $lead->update($validated);

        return redirect()->route('admin.leads.show', $lead)
            ->with('success', 'Lead updated successfully.');
    }

    /**
     * Remove the specified lead.
     */
    public function destroy(Lead $lead)
    {
        $lead->delete();

        return redirect()->route('admin.leads.index')
            ->with('success', 'Lead deleted successfully.');
    }

    /**
     * Export leads to CSV.
     */
    public function export(Request $request)
    {
        $query = Lead::with('landingPage');

        // Apply same filters as index
        if ($request->filled('landing_page_id')) {
            $query->where('landing_page_id', $request->landing_page_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('company_name', 'like', "%{$search}%");
            });
        }

        $leads = $query->orderBy('created_at', 'desc')->get();

        $filename = 'leads_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($leads) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'ID',
                'Landing Page',
                'Name',
                'Email',
                'Company',
                'Phone',
                'Quiz Score',
                'Availability',
                'PDF Sent',
                'Created At'
            ]);

            // CSV data
            foreach ($leads as $lead) {
                fputcsv($file, [
                    $lead->id,
                    $lead->landingPage->title,
                    $lead->name,
                    $lead->email,
                    $lead->company_name,
                    $lead->phone_number,
                    $lead->quiz_score,
                    $lead->availability,
                    $lead->pdf_sent ? 'Yes' : 'No',
                    $lead->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
