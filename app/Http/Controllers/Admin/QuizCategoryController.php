<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Quiz;
use App\Models\QuizCategory;
use App\Models\LandingPage;
use Illuminate\Http\Request;

class QuizCategoryController extends Controller
{
    /**
     * Display the categories for a quiz.
     */
    public function index(LandingPage $landingPage)
    {
        $quiz = $landingPage->quiz()->with(['categories.questions'])->firstOrFail();
        $categories = $quiz->categories()->ordered()->get();
        
        return view('admin.quiz-categories.index', compact('landingPage', 'quiz', 'categories'));
    }

    /**
     * Store a new category.
     */
    public function store(Request $request, LandingPage $landingPage)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
        ]);

        $quiz = $landingPage->quiz()->firstOrFail();

        // Get the next sort order
        $maxSortOrder = $quiz->categories()->max('sort_order') ?? 0;

        $category = QuizCategory::create([
            'quiz_id' => $quiz->id,
            'name' => $validated['name'],
            'description' => $validated['description'] ?? null,
            'color' => $validated['color'],
            'sort_order' => $maxSortOrder + 1,
            'is_active' => $validated['is_active'] ?? true,
        ]);

        return redirect()->route('admin.quiz-categories.index', $landingPage)
            ->with('success', 'Category created successfully.');
    }

    /**
     * Update a category.
     */
    public function update(Request $request, LandingPage $landingPage, QuizCategory $category)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
        ]);

        $category->update([
            'name' => $validated['name'],
            'description' => $validated['description'] ?? null,
            'color' => $validated['color'],
            'is_active' => $validated['is_active'] ?? true,
        ]);

        return redirect()->route('admin.quiz-categories.index', $landingPage)
            ->with('success', 'Category updated successfully.');
    }

    /**
     * Delete a category.
     */
    public function destroy(LandingPage $landingPage, QuizCategory $category)
    {
        // Check if category has questions
        if ($category->questions()->count() > 0) {
            return redirect()->route('admin.quiz-categories.index', $landingPage)
                ->with('error', 'Cannot delete category that contains questions. Please move or delete the questions first.');
        }

        $category->delete();

        return redirect()->route('admin.quiz-categories.index', $landingPage)
            ->with('success', 'Category deleted successfully.');
    }

    /**
     * Reorder categories.
     */
    public function reorder(Request $request, LandingPage $landingPage)
    {
        $validated = $request->validate([
            'categories' => 'required|array',
            'categories.*' => 'required|integer|exists:quiz_categories,id',
        ]);

        $quiz = $landingPage->quiz()->firstOrFail();

        foreach ($validated['categories'] as $index => $categoryId) {
            QuizCategory::where('id', $categoryId)
                ->where('quiz_id', $quiz->id)
                ->update(['sort_order' => $index + 1]);
        }

        return response()->json(['success' => true]);
    }
}
