<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;

class SettingsAdminController extends Controller
{
    /**
     * Display the settings page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        return view('admin.settings.index');
    }

    /**
     * Display the social media settings page.
     *
     * @return \Illuminate\View\View
     */
    public function socialMedia()
    {
        $socialMediaSettings = Setting::where('group', 'social_media')->get();
        return view('admin.settings.social-media', compact('socialMediaSettings'));
    }

    /**
     * Update social media settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateSocialMedia(Request $request)
    {
        $validated = $request->validate([
            'social_facebook' => 'nullable|url',
            'social_twitter' => 'nullable|url',
            'social_linkedin' => 'nullable|url',
            'social_instagram' => 'nullable|url',
        ]);

        foreach ($validated as $key => $value) {
            Setting::set($key, $value);
        }

        return redirect()->route('admin.settings.social-media')
            ->with('success', 'Social media settings updated successfully.');
    }
}
