<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LandingPage;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class LandingPageAdminController extends Controller
{
    /**
     * Display a listing of landing pages.
     */
    public function index()
    {
        $landingPages = LandingPage::withCount('leads')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.landing-pages.index', compact('landingPages'));
    }

    /**
     * Show the form for creating a new landing page.
     */
    public function create()
    {
        return view('admin.landing-pages.create');
    }

    /**
     * Store a newly created landing page.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:landing_pages,slug',
            'content' => 'required|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
            'has_quiz' => 'boolean',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Ensure slug is unique
        $originalSlug = $validated['slug'];
        $counter = 1;
        while (LandingPage::where('slug', $validated['slug'])->exists()) {
            $validated['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        $landingPage = LandingPage::create($validated);

        return redirect()->route('admin.landing-pages.show', $landingPage)
            ->with('success', 'Landing page created successfully.');
    }

    /**
     * Display the specified landing page.
     */
    public function show(LandingPage $landingPage)
    {
        $landingPage->load(['leads' => function ($query) {
            $query->latest()->take(10);
        }, 'quiz.questions']);

        $stats = [
            'total_leads' => $landingPage->leads()->count(),
            'leads_today' => $landingPage->leads()->whereDate('created_at', today())->count(),
            'leads_this_week' => $landingPage->leads()->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'leads_this_month' => $landingPage->leads()->whereMonth('created_at', now()->month)->count(),
            'avg_quiz_score' => $landingPage->leads()->whereNotNull('quiz_score')->avg('quiz_score'),
        ];

        return view('admin.landing-pages.show', compact('landingPage', 'stats'));
    }

    /**
     * Show the form for editing the landing page.
     */
    public function edit(LandingPage $landingPage)
    {
        return view('admin.landing-pages.edit', compact('landingPage'));
    }

    /**
     * Update the specified landing page.
     */
    public function update(Request $request, LandingPage $landingPage)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:landing_pages,slug,' . $landingPage->id,
            'content' => 'required|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
            'has_quiz' => 'boolean',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        $landingPage->update($validated);

        return redirect()->route('admin.landing-pages.show', $landingPage)
            ->with('success', 'Landing page updated successfully.');
    }

    /**
     * Remove the specified landing page.
     */
    public function destroy(LandingPage $landingPage)
    {
        $landingPage->delete();

        return redirect()->route('admin.landing-pages.index')
            ->with('success', 'Landing page deleted successfully.');
    }
}
