<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ConsultationStatus;
use Illuminate\Http\Request;

class ConsultationStatusAdminController extends Controller
{
    /**
     * Display a listing of the consultation statuses.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $statuses = ConsultationStatus::orderBy('sort_order')->get();
        return view('admin.consultation-statuses.index', compact('statuses'));
    }

    /**
     * Show the form for creating a new consultation status.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.consultation-statuses.create');
    }

    /**
     * Store a newly created consultation status in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:consultation_statuses',
            'color' => 'required|string|max:7',
            'description' => 'nullable|string',
            'is_default' => 'boolean',
            'sort_order' => 'required|integer|min:0',
        ]);

        // Check if this is the first status or if there's no default status
        $hasDefaultStatus = ConsultationStatus::where('is_default', true)->exists();
        $statusCount = ConsultationStatus::count();

        // If this is the first status or there's no default status, force it to be default
        if ($statusCount === 0 || !$hasDefaultStatus) {
            $validated['is_default'] = true;
        }

        $status = ConsultationStatus::create($validated);

        if ($validated['is_default']) {
            $status->setAsDefault();
        }

        return redirect()->route('admin.consultation-statuses.index')
            ->with('success', 'Consultation status created successfully.');
    }

    /**
     * Show the form for editing the specified consultation status.
     *
     * @param  \App\Models\ConsultationStatus  $consultationStatus
     * @return \Illuminate\View\View
     */
    public function edit(ConsultationStatus $consultationStatus)
    {
        return view('admin.consultation-statuses.edit', [
            'status' => $consultationStatus
        ]);
    }

    /**
     * Update the specified consultation status in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ConsultationStatus  $consultationStatus
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, ConsultationStatus $consultationStatus)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:consultation_statuses,name,' . $consultationStatus->id,
            'color' => 'required|string|max:7',
            'description' => 'nullable|string',
            'is_default' => 'boolean',
            'sort_order' => 'required|integer|min:0',
        ]);

        // Check if this status is currently the default and the user is trying to unset it
        if ($consultationStatus->is_default && !$request->boolean('is_default')) {
            // Count how many other statuses exist
            $otherStatusesCount = ConsultationStatus::where('id', '!=', $consultationStatus->id)->count();

            // If this is the only status or there are no other statuses that could be default
            if ($otherStatusesCount === 0) {
                // Force it to remain the default
                $validated['is_default'] = true;

                // Add a flash message to inform the user
                session()->flash('warning', 'This is the only status and must remain the default. Create another status first if you want to change the default.');
            }
        }

        $consultationStatus->update($validated);

        // If this status should be the default, set it
        if ($validated['is_default'] ?? $request->boolean('is_default')) {
            $consultationStatus->setAsDefault();
        }
        // If we're unsetting the default status, make sure there's another default
        elseif ($consultationStatus->is_default) {
            // Find another status to make default
            $newDefault = ConsultationStatus::where('id', '!=', $consultationStatus->id)->first();
            if ($newDefault) {
                $newDefault->setAsDefault();
            }
        }

        return redirect()->route('admin.consultation-statuses.index')
            ->with('success', 'Consultation status updated successfully.');
    }

    /**
     * Remove the specified consultation status from storage.
     *
     * @param  \App\Models\ConsultationStatus  $consultationStatus
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(ConsultationStatus $consultationStatus)
    {
        // Check if this is the default status
        if ($consultationStatus->is_default) {
            return redirect()->route('admin.consultation-statuses.index')
                ->with('error', 'Cannot delete the default status.');
        }

        // Check if there are consultations using this status
        if ($consultationStatus->consultations()->count() > 0) {
            return redirect()->route('admin.consultation-statuses.index')
                ->with('error', 'Cannot delete a status that is being used by consultations.');
        }

        // Check if this is the only status
        if (ConsultationStatus::count() <= 1) {
            return redirect()->route('admin.consultation-statuses.index')
                ->with('error', 'Cannot delete the only status. At least one status must exist.');
        }

        $consultationStatus->delete();

        // After deletion, check if there's still a default status
        if (!ConsultationStatus::where('is_default', true)->exists()) {
            // If no default status exists, set the first available status as default
            $firstStatus = ConsultationStatus::first();
            if ($firstStatus) {
                $firstStatus->setAsDefault();
                session()->flash('info', 'A new default status has been automatically set.');
            }
        }

        return redirect()->route('admin.consultation-statuses.index')
            ->with('success', 'Consultation status deleted successfully.');
    }

    /**
     * Set the specified consultation status as the default.
     *
     * @param  \App\Models\ConsultationStatus  $consultationStatus
     * @return \Illuminate\Http\RedirectResponse
     */
    public function setDefault(ConsultationStatus $consultationStatus)
    {
        $consultationStatus->setAsDefault();

        return redirect()->route('admin.consultation-statuses.index')
            ->with('success', 'Default status updated successfully.');
    }
}
