<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken;

class ApiTokenController extends Controller
{
    /**
     * Display the API tokens management page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $users = User::has('tokens')->orWhere('is_admin', true)->get();
        $tokens = PersonalAccessToken::with('tokenable')->latest()->get();

        return view('admin.settings.api-tokens', compact('users', 'tokens'));
    }

    /**
     * Create a new API token.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'token_name' => 'required|string|max:255',
            'abilities' => 'nullable|array',
            'abilities.*' => 'string|in:create,read,update,delete',
            'expiration' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return redirect()->route('admin.settings.api-tokens')
                ->withErrors($validator)
                ->withInput();
        }

        $user = User::findOrFail($request->user_id);
        $abilities = $request->abilities ?? ['*'];

        // Create token with expiration if specified
        $tokenExpiration = (int)($request->expiration ?: Setting::get('api_token_expiration', 0));
        $expiresAt = $tokenExpiration > 0 ? now()->addDays($tokenExpiration) : null;

        $token = $user->createToken(
            $request->token_name,
            $abilities,
            $expiresAt
        );

        return redirect()->route('admin.settings.api-tokens')
            ->with('success', 'API token created successfully.')
            ->with('plain_text_token', $token->plainTextToken);
    }

    /**
     * Revoke an API token.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $token = PersonalAccessToken::findOrFail($id);
        $token->delete();

        return redirect()->route('admin.settings.api-tokens')
            ->with('success', 'API token revoked successfully.');
    }
}
