<?php

namespace App\Http\Controllers;

use App\Models\Consultation;
use App\Models\ConsultationStatus;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ConsultationController extends Controller
{
    /**
     * Store a new consultation request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'website_url' => 'nullable|url|max:255',
            'business_description' => 'required|string',
            'pain_point' => 'required|string',
            'current_automation' => 'required|string',
            'additional_notes' => 'nullable|string',
        ]);

        // Get the default status (usually "Pending")
        $defaultStatus = ConsultationStatus::getDefault();
        if ($defaultStatus) {
            $validated['status_id'] = $defaultStatus->id;
        }

        $consultation = Consultation::create($validated);
        $consultation->load('status');

        return response()->json([
            'success' => true,
            'message' => 'Consultation request received successfully.',
            'consultation' => $consultation,
        ]);
    }
}
