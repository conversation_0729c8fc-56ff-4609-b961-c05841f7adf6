<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Consultation;
use App\Models\ConsultationStatus;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Database\Eloquent\Builder;

class ConsultationApiController extends Controller
{
    /**
     * Display a listing of the consultations with optional filtering.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = Consultation::with('status');

        // Apply filters if provided

        // Filter by name (partial match)
        if ($request->has('name')) {
            $query->where('name', 'like', '%' . $request->name . '%');
        }

        // Filter by email (partial match)
        if ($request->has('email')) {
            $query->where('email', 'like', '%' . $request->email . '%');
        }

        // Filter by website URL (partial match)
        if ($request->has('website_url')) {
            $query->where('website_url', 'like', '%' . $request->website_url . '%');
        }

        // Filter by business description (partial match)
        if ($request->has('business_description')) {
            $query->where('business_description', 'like', '%' . $request->business_description . '%');
        }

        // Filter by pain point (partial match)
        if ($request->has('pain_point')) {
            $query->where('pain_point', 'like', '%' . $request->pain_point . '%');
        }

        // Filter by current automation (partial match)
        if ($request->has('current_automation')) {
            $query->where('current_automation', 'like', '%' . $request->current_automation . '%');
        }

        // Filter by status (exact match by ID or name)
        if ($request->has('status_id')) {
            $query->where('status_id', $request->status_id);
        } elseif ($request->has('status')) {
            $query->whereHas('status', function($q) use ($request) {
                $q->where('name', $request->status);
            });
        }

        // For backward compatibility - filter by contacted status
        if ($request->has('contacted')) {
            $isContacted = $request->boolean('contacted');
            if ($isContacted) {
                $query->whereHas('status', function($q) {
                    $q->where('name', 'Contacted');
                });
            } else {
                $query->whereHas('status', function($q) {
                    $q->where('name', 'Pending');
                });
            }
        }

        // Filter by created date range
        if ($request->has('created_from')) {
            $query->whereDate('created_at', '>=', $request->created_from);
        }

        if ($request->has('created_to')) {
            $query->whereDate('created_at', '<=', $request->created_to);
        }

        // Apply sorting
        $sortField = $request->input('sort_by', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');

        // Validate sort field to prevent SQL injection
        $allowedSortFields = ['id', 'name', 'email', 'created_at', 'updated_at', 'status_id'];
        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'created_at';
        }

        // Validate sort direction
        if (!in_array(strtolower($sortDirection), ['asc', 'desc'])) {
            $sortDirection = 'desc';
        }

        $query->orderBy($sortField, $sortDirection);

        // Apply pagination if requested
        $perPage = $request->input('per_page', 15);
        if ($perPage > 100) $perPage = 100; // Limit maximum items per page

        if ($request->has('page')) {
            $consultations = $query->paginate($perPage);
            return response()->json([
                'success' => true,
                'data' => $consultations->items(),
                'meta' => [
                    'current_page' => $consultations->currentPage(),
                    'last_page' => $consultations->lastPage(),
                    'per_page' => $consultations->perPage(),
                    'total' => $consultations->total()
                ]
            ]);
        } else {
            // If no pagination requested, get all results
            $consultations = $query->get();
            return response()->json([
                'success' => true,
                'data' => $consultations,
            ]);
        }
    }

    /**
     * Store a newly created consultation in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'website_url' => 'nullable|url|max:255',
            'business_description' => 'required|string',
            'pain_point' => 'required|string',
            'current_automation' => 'required|string',
            'additional_notes' => 'nullable|string',
            'status_id' => 'nullable|exists:consultation_statuses,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $validator->validated();

        // If status_id is not provided, use the default status
        if (!isset($data['status_id'])) {
            $defaultStatus = ConsultationStatus::getDefault();
            if ($defaultStatus) {
                $data['status_id'] = $defaultStatus->id;
            }
        }

        $consultation = Consultation::create($data);
        $consultation->load('status');

        return response()->json([
            'success' => true,
            'message' => 'Consultation created successfully',
            'data' => $consultation,
        ], 201);
    }

    /**
     * Display the specified consultation.
     *
     * @param  \App\Models\Consultation  $consultation
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Consultation $consultation): JsonResponse
    {
        $consultation->load('status');

        return response()->json([
            'success' => true,
            'data' => $consultation,
        ]);
    }

    /**
     * Update the specified consultation in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Consultation  $consultation
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, Consultation $consultation): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'website_url' => 'nullable|url|max:255',
            'business_description' => 'required|string',
            'pain_point' => 'required|string',
            'current_automation' => 'required|string',
            'additional_notes' => 'nullable|string',
            'status_id' => 'nullable|exists:consultation_statuses,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        // For backward compatibility - handle 'contacted' field
        if ($request->has('contacted') && !$request->has('status_id')) {
            $isContacted = $request->boolean('contacted');
            if ($isContacted) {
                $contactedStatus = ConsultationStatus::where('name', 'Contacted')->first();
                if ($contactedStatus) {
                    $request->merge(['status_id' => $contactedStatus->id]);
                }
            } else {
                $pendingStatus = ConsultationStatus::where('name', 'Pending')->first();
                if ($pendingStatus) {
                    $request->merge(['status_id' => $pendingStatus->id]);
                }
            }
        }

        $consultation->update($validator->validated());
        $consultation->load('status');

        return response()->json([
            'success' => true,
            'message' => 'Consultation updated successfully',
            'data' => $consultation,
        ]);
    }

    /**
     * Remove the specified consultation from storage.
     *
     * @param  \App\Models\Consultation  $consultation
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Consultation $consultation): JsonResponse
    {
        $consultation->delete();

        return response()->json([
            'success' => true,
            'message' => 'Consultation deleted successfully',
        ]);
    }

    /**
     * Update the status of a consultation.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Consultation  $consultation
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, Consultation $consultation): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'status_id' => 'required|exists:consultation_statuses,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $consultation->update(['status_id' => $request->status_id]);
        $consultation->load('status');

        return response()->json([
            'success' => true,
            'message' => 'Consultation status updated successfully',
            'data' => $consultation,
        ]);
    }

    /**
     * Mark a consultation as contacted (for backward compatibility).
     *
     * @param  \App\Models\Consultation  $consultation
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsContacted(Consultation $consultation): JsonResponse
    {
        $contactedStatus = ConsultationStatus::where('name', 'Contacted')->first();

        if (!$contactedStatus) {
            return response()->json([
                'success' => false,
                'message' => 'Contacted status not found',
            ], 404);
        }

        $consultation->update(['status_id' => $contactedStatus->id]);
        $consultation->load('status');

        return response()->json([
            'success' => true,
            'message' => 'Consultation marked as contacted',
            'data' => $consultation,
        ]);
    }
}
