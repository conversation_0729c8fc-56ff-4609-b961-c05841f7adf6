<?php

namespace App\Http\Controllers;

use App\Models\LandingPage;
use App\Models\Lead;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class LandingPageController extends Controller
{
    /**
     * Display the landing page.
     */
    public function show(LandingPage $landingPage)
    {
        if (!$landingPage->is_active) {
            abort(404);
        }

        return view('landing-pages.show', compact('landingPage'));
    }

    /**
     * Display the quiz for the landing page.
     */
    public function quiz(LandingPage $landingPage)
    {
        if (!$landingPage->is_active || !$landingPage->has_quiz) {
            abort(404);
        }

        $quiz = $landingPage->quiz()->with(['questions.category', 'categories'])->first();

        if (!$quiz || !$quiz->is_active) {
            abort(404);
        }

        return view('landing-pages.quiz', compact('landingPage', 'quiz'));
    }

    /**
     * Process the quiz submission.
     */
    public function submitQuiz(Request $request, LandingPage $landingPage)
    {
        if (!$landingPage->is_active || !$landingPage->has_quiz) {
            abort(404);
        }

        $quiz = $landingPage->quiz()->with(['questions.category', 'categories'])->first();

        if (!$quiz || !$quiz->is_active) {
            abort(404);
        }

        // Validate basic lead information
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'company_name' => 'required|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'quiz_responses' => 'required|array',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Validate quiz responses
        $quizResponses = $request->input('quiz_responses', []);
        $quizValidationResult = $quiz->validateResponses($quizResponses);

        if (!$quizValidationResult['valid']) {
            return redirect()->back()
                ->withErrors(['quiz_responses' => $quizValidationResult['errors']])
                ->withInput();
        }

        // Calculate quiz score
        $score = $quiz->calculateScore($quizResponses);

        // Create the lead
        $lead = Lead::create([
            'landing_page_id' => $landingPage->id,
            'name' => $request->input('name'),
            'email' => $request->input('email'),
            'company_name' => $request->input('company_name'),
            'phone_number' => $request->input('phone_number'),
            'quiz_score' => $score,
            'quiz_responses' => $quizResponses,
        ]);

        // Store lead ID in session for the next step
        session(['lead_id' => $lead->id]);

        // TODO: Generate and send PDF report based on quiz results

        return redirect()->route('landing-pages.schedule', $landingPage);
    }

    /**
     * Display the consultation scheduling page.
     */
    public function schedule(LandingPage $landingPage)
    {
        if (!$landingPage->is_active) {
            abort(404);
        }

        $leadId = session('lead_id');
        if (!$leadId) {
            return redirect()->route('landing-pages.show', $landingPage);
        }

        $lead = Lead::find($leadId);
        if (!$lead || $lead->landing_page_id !== $landingPage->id) {
            return redirect()->route('landing-pages.show', $landingPage);
        }

        // Get quiz configuration for score display
        $quiz = $landingPage->quiz;
        $scoreData = null;

        if ($quiz && $lead->quiz_score !== null) {
            $scoreData = [
                'score' => $lead->quiz_score,
                'gauge_config' => $quiz->getGaugeConfig(),
                'result_text' => $quiz->getResultTextForScore($lead->quiz_score),
                'gauge_color' => $quiz->getGaugeColorForScore($lead->quiz_score),
                'gauge_label' => $quiz->getGaugeLabelForScore($lead->quiz_score),
            ];
        }

        return view('landing-pages.schedule', compact('landingPage', 'lead', 'scoreData'));
    }

    /**
     * Process the consultation scheduling.
     */
    public function submitSchedule(Request $request, LandingPage $landingPage)
    {
        $leadId = session('lead_id');
        if (!$leadId) {
            return redirect()->route('landing-pages.show', $landingPage);
        }

        $lead = Lead::find($leadId);
        if (!$lead || $lead->landing_page_id !== $landingPage->id) {
            return redirect()->route('landing-pages.show', $landingPage);
        }

        $validator = Validator::make($request->all(), [
            'availability' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update lead with availability
        $lead->update([
            'availability' => $request->input('availability'),
        ]);

        // Clear session
        session()->forget('lead_id');

        return view('landing-pages.thank-you', compact('landingPage', 'lead'));
    }
}
