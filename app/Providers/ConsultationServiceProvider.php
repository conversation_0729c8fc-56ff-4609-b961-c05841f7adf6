<?php

namespace App\Providers;

use App\Models\ConsultationStatus;
use Illuminate\Support\ServiceProvider;

class ConsultationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Ensure there's always a default consultation status
        // Only run this in web requests, not during console commands
        if (!$this->app->runningInConsole()) {
            try {
                ConsultationStatus::ensureDefaultStatusExists();
            } catch (\Exception $e) {
                // Log the error but don't crash the application
                // This might happen if the database isn't set up yet
                logger()->error('Failed to ensure default consultation status: ' . $e->getMessage());
            }
        }
    }
}
