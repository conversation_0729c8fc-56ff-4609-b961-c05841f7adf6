<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Add the RedirectToWww middleware to the global middleware stack
        // This ensures it runs for all requests, including those that bypass the web middleware group
        $middleware->prepend(\App\Http\Middleware\RedirectToWww::class);

        // Also add it to the web middleware group for good measure
        $middleware->web([\App\Http\Middleware\RedirectToWww::class]);

        $middleware->alias([
            'admin' => \App\Http\Middleware\AdminMiddleware::class,
            'ip.restrict' => \App\Http\Middleware\IpRestrictionMiddleware::class,
        ]);

        // Configure redirect paths for authenticated and guest users
        $middleware->redirectTo(
            guests: fn () => route('login'),
            users: function ($request) {
                return $request->user()->is_admin
                    ? route('admin.dashboard')
                    : route('settings.profile');
            }
        );
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
