<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('consultations', function (Blueprint $table) {
            // Add the new status_id column
            $table->foreignId('status_id')->nullable()->after('additional_notes');
            
            // Add foreign key constraint
            $table->foreign('status_id')
                ->references('id')
                ->on('consultation_statuses')
                ->onDelete('set null');
        });

        // Get the ID of the default statuses
        $pendingStatusId = DB::table('consultation_statuses')
            ->where('name', 'Pending')
            ->value('id');
            
        $contactedStatusId = DB::table('consultation_statuses')
            ->where('name', 'Contacted')
            ->value('id');

        // Migrate existing data
        if ($pendingStatusId && $contactedStatusId) {
            // Update all existing consultations based on their 'contacted' value
            DB::table('consultations')
                ->where('contacted', false)
                ->update(['status_id' => $pendingStatusId]);
                
            DB::table('consultations')
                ->where('contacted', true)
                ->update(['status_id' => $contactedStatusId]);
        }

        // Now that we've migrated the data, we can drop the old column
        Schema::table('consultations', function (Blueprint $table) {
            $table->dropColumn('contacted');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('consultations', function (Blueprint $table) {
            // Add back the contacted column
            $table->boolean('contacted')->default(false)->after('additional_notes');
        });

        // Get the IDs of the statuses
        $contactedStatusId = DB::table('consultation_statuses')
            ->where('name', 'Contacted')
            ->value('id');

        // Migrate data back
        if ($contactedStatusId) {
            // Set contacted = true for all consultations with the "Contacted" status
            DB::table('consultations')
                ->where('status_id', $contactedStatusId)
                ->update(['contacted' => true]);
        }

        // Drop the status_id column and its foreign key
        Schema::table('consultations', function (Blueprint $table) {
            $table->dropForeign(['status_id']);
            $table->dropColumn('status_id');
        });
    }
};
