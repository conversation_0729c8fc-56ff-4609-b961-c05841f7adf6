<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('leads', function (Blueprint $table) {
            $table->id();
            $table->foreignId('landing_page_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('email');
            $table->string('company_name');
            $table->string('phone_number')->nullable();
            $table->text('availability')->nullable(); // Free text for availability
            $table->integer('quiz_score')->nullable();
            $table->json('quiz_responses')->nullable(); // Store quiz responses as JSON
            $table->boolean('pdf_sent')->default(false);
            $table->timestamp('pdf_sent_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('leads');
    }
};
