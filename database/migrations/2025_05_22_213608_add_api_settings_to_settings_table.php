<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add API settings to the settings table
        $apiSettings = [
            [
                'key' => 'allowed_api_ips',
                'value' => '127.0.0.1,::1',
                'group' => 'api',
                'type' => 'text',
                'label' => 'Allowed API IPs',
                'description' => 'Comma-separated list of IP addresses allowed to access the API',
            ],
            [
                'key' => 'api_token_expiration',
                'value' => '0', // 0 means no expiration
                'group' => 'api',
                'type' => 'number',
                'label' => 'API Token Expiration (days)',
                'description' => 'Number of days before API tokens expire (0 for no expiration)',
            ],
            [
                'key' => 'api_rate_limit',
                'value' => '60',
                'group' => 'api',
                'type' => 'number',
                'label' => 'API Rate Limit',
                'description' => 'Maximum number of API requests per minute',
            ],
        ];

        foreach ($apiSettings as $setting) {
            DB::table('settings')->insert($setting);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove API settings
        DB::table('settings')->where('group', 'api')->delete();
    }
};
