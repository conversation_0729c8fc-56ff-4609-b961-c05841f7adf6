<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('group')->default('general');
            $table->string('type')->default('text');
            $table->string('label')->nullable();
            $table->text('description')->nullable();
            $table->timestamps();
        });

        // Insert default social media settings
        DB::table('settings')->insert([
            [
                'key' => 'social_facebook',
                'value' => null,
                'group' => 'social_media',
                'type' => 'url',
                'label' => 'Facebook URL',
                'description' => 'Enter your Facebook page URL',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'social_twitter',
                'value' => null,
                'group' => 'social_media',
                'type' => 'url',
                'label' => 'Twitter URL',
                'description' => 'Enter your Twitter profile URL',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'social_linkedin',
                'value' => null,
                'group' => 'social_media',
                'type' => 'url',
                'label' => 'LinkedIn URL',
                'description' => 'Enter your LinkedIn profile URL',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'social_instagram',
                'value' => null,
                'group' => 'social_media',
                'type' => 'url',
                'label' => 'Instagram URL',
                'description' => 'Enter your Instagram profile URL',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
