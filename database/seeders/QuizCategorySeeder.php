<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Quiz;
use App\Models\QuizCategory;
use App\Models\QuizQuestion;

class QuizCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first quiz (from the landing page seeder)
        $quiz = Quiz::first();
        
        if (!$quiz) {
            $this->command->info('No quiz found. Please run the LandingPageSeeder first.');
            return;
        }

        // Create categories
        $categories = [
            [
                'name' => 'Business Overview',
                'description' => 'Questions about your company size and structure',
                'color' => '#007bff',
                'sort_order' => 1,
            ],
            [
                'name' => 'Current Processes',
                'description' => 'Understanding your existing business processes',
                'color' => '#28a745',
                'sort_order' => 2,
            ],
            [
                'name' => 'Technology Assessment',
                'description' => 'Evaluating your current technology infrastructure',
                'color' => '#ffc107',
                'sort_order' => 3,
            ],
            [
                'name' => 'Challenges & Goals',
                'description' => 'Identifying pain points and automation opportunities',
                'color' => '#dc3545',
                'sort_order' => 4,
            ],
        ];

        foreach ($categories as $categoryData) {
            QuizCategory::create([
                'quiz_id' => $quiz->id,
                'name' => $categoryData['name'],
                'description' => $categoryData['description'],
                'color' => $categoryData['color'],
                'sort_order' => $categoryData['sort_order'],
                'is_active' => true,
            ]);
        }

        // Assign existing questions to categories
        $businessOverview = QuizCategory::where('quiz_id', $quiz->id)->where('name', 'Business Overview')->first();
        $currentProcesses = QuizCategory::where('quiz_id', $quiz->id)->where('name', 'Current Processes')->first();
        $technologyAssessment = QuizCategory::where('quiz_id', $quiz->id)->where('name', 'Technology Assessment')->first();
        $challengesGoals = QuizCategory::where('quiz_id', $quiz->id)->where('name', 'Challenges & Goals')->first();

        // Update existing questions with categories
        $questions = $quiz->questions()->get();
        
        foreach ($questions as $question) {
            $categoryId = null;
            
            // Assign categories based on question content
            if (str_contains(strtolower($question->question), 'company') || 
                str_contains(strtolower($question->question), 'size') ||
                str_contains(strtolower($question->question), 'employees')) {
                $categoryId = $businessOverview->id;
            } elseif (str_contains(strtolower($question->question), 'process') ||
                      str_contains(strtolower($question->question), 'workflow') ||
                      str_contains(strtolower($question->question), 'manual')) {
                $categoryId = $currentProcesses->id;
            } elseif (str_contains(strtolower($question->question), 'technology') ||
                      str_contains(strtolower($question->question), 'infrastructure') ||
                      str_contains(strtolower($question->question), 'system')) {
                $categoryId = $technologyAssessment->id;
            } elseif (str_contains(strtolower($question->question), 'challenge') ||
                      str_contains(strtolower($question->question), 'problem') ||
                      str_contains(strtolower($question->question), 'biggest')) {
                $categoryId = $challengesGoals->id;
            }
            
            if ($categoryId) {
                $question->update(['category_id' => $categoryId]);
            }
        }

        $this->command->info('Quiz categories created and questions categorized successfully!');
    }
}
