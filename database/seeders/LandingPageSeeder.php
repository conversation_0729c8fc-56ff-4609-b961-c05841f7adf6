<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\LandingPage;
use App\Models\Quiz;
use App\Models\QuizQuestion;
use App\Models\Lead;

class LandingPageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a sample landing page
        $landingPage = LandingPage::create([
            'title' => 'Business Automation Assessment',
            'slug' => 'business-automation-assessment',
            'content' => '
                <div class="hero-section text-center mb-5">
                    <h1 class="display-4 fw-bold text-primary mb-4">Transform Your Business with Automation</h1>
                    <p class="lead mb-4">Discover how automation can streamline your operations, reduce costs, and boost productivity. Take our comprehensive assessment to get personalized recommendations for your business.</p>
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="benefits-grid">
                                <div class="row">
                                    <div class="col-md-4 mb-4">
                                        <div class="benefit-item text-center">
                                            <i class="fas fa-clock fa-3x text-primary mb-3"></i>
                                            <h5>Save Time</h5>
                                            <p>Automate repetitive tasks and focus on what matters most</p>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-4">
                                        <div class="benefit-item text-center">
                                            <i class="fas fa-dollar-sign fa-3x text-primary mb-3"></i>
                                            <h5>Reduce Costs</h5>
                                            <p>Lower operational expenses through efficient automation</p>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-4">
                                        <div class="benefit-item text-center">
                                            <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                                            <h5>Boost Growth</h5>
                                            <p>Scale your business faster with automated processes</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="assessment-info bg-light p-5 rounded mb-5">
                    <h2 class="text-center mb-4">What You\'ll Get</h2>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-3"><i class="fas fa-check text-success me-2"></i> Personalized automation roadmap</li>
                                <li class="mb-3"><i class="fas fa-check text-success me-2"></i> Cost-benefit analysis</li>
                                <li class="mb-3"><i class="fas fa-check text-success me-2"></i> Implementation timeline</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-3"><i class="fas fa-check text-success me-2"></i> Free consultation call</li>
                                <li class="mb-3"><i class="fas fa-check text-success me-2"></i> Detailed PDF report</li>
                                <li class="mb-3"><i class="fas fa-check text-success me-2"></i> No obligation assessment</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="cta-section text-center">
                    <h3 class="mb-4">Ready to Transform Your Business?</h3>
                    <p class="mb-4">Take our 5-minute assessment to discover your automation potential.</p>
                </div>
            ',
            'meta_title' => 'Business Automation Assessment - Free Analysis',
            'meta_description' => 'Get a free business automation assessment and discover how to streamline your operations, reduce costs, and boost productivity.',
            'is_active' => true,
            'has_quiz' => true,
        ]);

        // Create a quiz for the landing page
        $quiz = Quiz::create([
            'landing_page_id' => $landingPage->id,
            'title' => 'Business Automation Readiness Assessment',
            'description' => 'Answer these questions to help us understand your current business processes and automation needs.',
            'is_active' => true,
        ]);

        // Create quiz questions
        $questions = [
            [
                'question' => 'What is the size of your company?',
                'type' => 'single_choice',
                'options' => [
                    'small' => ['text' => '1-10 employees', 'points' => 5],
                    'medium' => ['text' => '11-50 employees', 'points' => 10],
                    'large' => ['text' => '51-200 employees', 'points' => 15],
                    'enterprise' => ['text' => '200+ employees', 'points' => 20],
                ],
                'points' => 20,
                'sort_order' => 1,
            ],
            [
                'question' => 'How many hours per week does your team spend on repetitive tasks?',
                'type' => 'single_choice',
                'options' => [
                    'low' => ['text' => 'Less than 5 hours', 'points' => 5],
                    'medium' => ['text' => '5-15 hours', 'points' => 10],
                    'high' => ['text' => '15-30 hours', 'points' => 15],
                    'very_high' => ['text' => 'More than 30 hours', 'points' => 20],
                ],
                'points' => 20,
                'sort_order' => 2,
            ],
            [
                'question' => 'Which business processes would you most like to automate? (Select all that apply)',
                'type' => 'multiple_choice',
                'options' => [
                    'data_entry' => ['text' => 'Data entry and processing', 'points' => 5],
                    'customer_service' => ['text' => 'Customer service and support', 'points' => 5],
                    'marketing' => ['text' => 'Marketing and lead generation', 'points' => 5],
                    'accounting' => ['text' => 'Accounting and invoicing', 'points' => 5],
                    'inventory' => ['text' => 'Inventory management', 'points' => 5],
                    'reporting' => ['text' => 'Reporting and analytics', 'points' => 5],
                ],
                'points' => 30,
                'sort_order' => 3,
            ],
            [
                'question' => 'How would you rate your current technology infrastructure?',
                'type' => 'scale',
                'options' => null,
                'points' => 10,
                'sort_order' => 4,
            ],
            [
                'question' => 'What is your biggest challenge with current business processes?',
                'type' => 'text',
                'options' => null,
                'points' => 10,
                'sort_order' => 5,
            ],
        ];

        foreach ($questions as $questionData) {
            QuizQuestion::create([
                'quiz_id' => $quiz->id,
                'question' => $questionData['question'],
                'type' => $questionData['type'],
                'options' => $questionData['options'],
                'points' => $questionData['points'],
                'sort_order' => $questionData['sort_order'],
                'is_required' => true,
            ]);
        }

        // Create some sample leads
        $sampleLeads = [
            [
                'name' => 'John Smith',
                'email' => '<EMAIL>',
                'company_name' => 'Tech Solutions Inc',
                'phone_number' => '+****************',
                'quiz_score' => 75,
                'availability' => 'Monday-Friday 9 AM - 5 PM EST. Prefer Tuesday/Thursday mornings.',
                'quiz_responses' => [
                    '1' => 'medium',
                    '2' => 'high',
                    '3' => ['data_entry', 'marketing', 'reporting'],
                    '4' => '7',
                    '5' => 'Manual data entry takes too much time and is error-prone.',
                ],
            ],
            [
                'name' => 'Sarah Johnson',
                'email' => '<EMAIL>',
                'company_name' => 'Business Corp',
                'phone_number' => '+****************',
                'quiz_score' => 85,
                'availability' => 'Available weekdays 10 AM - 4 PM PST. Best times are Wednesday/Friday.',
                'quiz_responses' => [
                    '1' => 'large',
                    '2' => 'very_high',
                    '3' => ['customer_service', 'accounting', 'inventory'],
                    '4' => '8',
                    '5' => 'Customer service response times are too slow and inconsistent.',
                ],
            ],
            [
                'name' => 'Mike Davis',
                'email' => '<EMAIL>',
                'company_name' => 'Startup Innovations',
                'phone_number' => null,
                'quiz_score' => 60,
                'availability' => 'Flexible schedule. Available most days between 11 AM - 6 PM EST.',
                'quiz_responses' => [
                    '1' => 'small',
                    '2' => 'medium',
                    '3' => ['marketing', 'reporting'],
                    '4' => '6',
                    '5' => 'Need better tracking of marketing campaigns and ROI.',
                ],
            ],
        ];

        foreach ($sampleLeads as $leadData) {
            Lead::create([
                'landing_page_id' => $landingPage->id,
                'name' => $leadData['name'],
                'email' => $leadData['email'],
                'company_name' => $leadData['company_name'],
                'phone_number' => $leadData['phone_number'],
                'quiz_score' => $leadData['quiz_score'],
                'availability' => $leadData['availability'],
                'quiz_responses' => $leadData['quiz_responses'],
                'pdf_sent' => false,
                'created_at' => now()->subDays(rand(1, 30)),
            ]);
        }
    }
}
